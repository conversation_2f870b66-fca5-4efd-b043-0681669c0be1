2025-07-08 02:01:55,857 [INFO] ======================================================================
2025-07-08 02:01:55,857 [INFO] 🏥 传统健康智慧时钟 v1.1.1 跟踪框修复版 - 编译日志
2025-07-08 02:01:55,857 [INFO] ======================================================================
2025-07-08 02:01:55,857 [INFO] 📅 编译开始时间: 2025-07-08 02:01:55
2025-07-08 02:01:55,857 [INFO] 🔧 修复版本: v1.1.1 - 跟踪框修复完成
2025-07-08 02:01:55,857 [INFO] 🐍 Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-07-08 02:01:55,857 [INFO] 💻 工作目录: D:\项目文件夹\py
2025-07-08 02:01:55,857 [INFO] 📝 日志文件: compile_logs\compile_log_20250708_020155.txt
2025-07-08 02:01:55,858 [INFO] ----------------------------------------------------------------------
2025-07-08 02:01:55,858 [INFO] 🚀 开始传统健康智慧时钟 v1.1.1 跟踪框修复版编译
2025-07-08 02:01:55,858 [INFO] 🔨 步骤 1/2: 执行编译...
2025-07-08 02:01:55,858 [INFO] 🚀 开始编译...
2025-07-08 02:01:55,858 [INFO] ⏰ 编译可能需要几分钟时间，请耐心等待...
2025-07-08 02:01:55,858 [INFO] 🧹 清理旧的编译文件...
2025-07-08 02:01:55,873 [INFO]    🗑️ 删除: build
2025-07-08 02:01:56,094 [INFO]    🗑️ 删除: dist
2025-07-08 02:01:56,095 [INFO] 🔨 执行编译命令: py -3.11 -m PyInstaller --clean --noconfirm 传统健康智慧时钟_v1.1.1_跟踪框修复版.spec
2025-07-08 02:01:56,095 [INFO] --------------------------------------------------
2025-07-08 02:04:37,733 [INFO] --------------------------------------------------
2025-07-08 02:04:37,733 [INFO] ⏱️ 编译耗时: 161.64 秒
2025-07-08 02:04:37,734 [INFO] ✅ 编译成功！
2025-07-08 02:04:37,734 [INFO] 🔍 步骤 2/2: 验证编译结果...
2025-07-08 02:04:37,734 [INFO] 🔍 [验证] 检查编译后的程序...
2025-07-08 02:04:37,735 [INFO] 📁 [验证] 可执行文件: dist/传统健康智慧时钟_v1.1.1_跟踪框修复版\传统健康智慧时钟_v1.1.1_跟踪框修复版.exe
2025-07-08 02:04:37,735 [INFO] 📊 [验证] 文件大小: 29.2MB
2025-07-08 02:04:37,735 [INFO] ✅ [验证] 健康监测模块已包含
2025-07-08 02:04:37,735 [INFO] 🎉 [验证完成] v1.1.1跟踪框修复版编译验证成功！
2025-07-08 02:04:37,736 [INFO] ✅ 编译完成！
2025-07-08 02:04:37,736 [INFO] 🎉 v1.1.1跟踪框修复版编译完成！
2025-07-08 02:04:37,736 [INFO] 🎯 人脸检测框功能已完全修复
