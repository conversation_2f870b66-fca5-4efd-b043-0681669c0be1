#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地SkinDetector实现
Enhanced Skin Detection Implementation
"""

import cv2
import numpy as np
from typing import Optional, Tuple, List, Dict, Any

class SkinDetector:
    """本地皮肤检测器实现"""
    
    def __init__(self):
        """初始化皮肤检测器"""
        # 主要皮肤色彩范围
        self.lower_skin = np.array([0, 20, 70], dtype=np.uint8)
        self.upper_skin = np.array([20, 255, 255], dtype=np.uint8)
        
        # 额外的皮肤色彩范围（处理不同肤色）
        self.lower_skin2 = np.array([170, 20, 70], dtype=np.uint8)
        self.upper_skin2 = np.array([180, 255, 255], dtype=np.uint8)
        
        # 更广泛的皮肤色彩范围
        self.lower_skin3 = np.array([0, 30, 60], dtype=np.uint8)
        self.upper_skin3 = np.array([25, 255, 255], dtype=np.uint8)
        
        print("✅ SkinDetector初始化成功")
    
    def detect(self, image: np.ndarray) -> np.ndarray:
        """检测皮肤区域"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 创建多个皮肤掩码
            mask1 = cv2.inRange(hsv, self.lower_skin, self.upper_skin)
            mask2 = cv2.inRange(hsv, self.lower_skin2, self.upper_skin2)
            mask3 = cv2.inRange(hsv, self.lower_skin3, self.upper_skin3)
            
            # 合并掩码
            skin_mask = cv2.bitwise_or(mask1, mask2)
            skin_mask = cv2.bitwise_or(skin_mask, mask3)
            
            # 形态学操作改善掩码质量
            kernel = np.ones((3, 3), np.uint8)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
            
            # 高斯模糊减少噪声
            skin_mask = cv2.GaussianBlur(skin_mask, (3, 3), 0)
            
            return skin_mask
            
        except Exception as e:
            print(f"❌ 皮肤检测失败: {e}")
            return np.zeros(image.shape[:2], dtype=np.uint8)
    
    def detect_regions(self, image: np.ndarray) -> List[np.ndarray]:
        """检测皮肤区域并返回轮廓"""
        try:
            skin_mask = self.detect(image)
            
            # 查找轮廓
            contours, _ = cv2.findContours(skin_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤小区域
            min_area = 1000
            valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
            
            return valid_contours
            
        except Exception as e:
            print(f"❌ 区域检测失败: {e}")
            return []
    
    def get_skin_percentage(self, image: np.ndarray) -> float:
        """获取皮肤覆盖率"""
        try:
            skin_mask = self.detect(image)
            total_pixels = image.shape[0] * image.shape[1]
            skin_pixels = np.sum(skin_mask > 0)
            
            return skin_pixels / total_pixels if total_pixels > 0 else 0.0
            
        except Exception as e:
            print(f"❌ 覆盖率计算失败: {e}")
            return 0.0
    
    def analyze_skin_health(self, image: np.ndarray) -> Dict[str, Any]:
        """分析皮肤健康状态"""
        try:
            skin_mask = self.detect(image)
            
            # 计算基础指标
            total_pixels = image.shape[0] * image.shape[1]
            skin_pixels = np.sum(skin_mask > 0)
            skin_coverage = skin_pixels / total_pixels if total_pixels > 0 else 0.0
            
            # 计算平均颜色
            masked_image = image.copy()
            masked_image[skin_mask == 0] = 0
            mean_color = cv2.mean(masked_image, mask=skin_mask)[:3]
            
            # 计算亮度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            masked_gray = gray * (skin_mask / 255.0)
            brightness = np.mean(masked_gray[skin_mask > 0]) if np.any(skin_mask > 0) else 0
            
            # 计算饱和度
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            saturation = np.mean(hsv[:, :, 1][skin_mask > 0]) if np.any(skin_mask > 0) else 0
            
            # 计算均匀性
            uniformity = self._calculate_uniformity(image, skin_mask)
            
            return {
                "skin_coverage": float(skin_coverage),
                "mean_color": mean_color,
                "brightness": float(brightness),
                "saturation": float(saturation),
                "uniformity": float(uniformity),
                "total_skin_pixels": int(skin_pixels)
            }
            
        except Exception as e:
            print(f"❌ 皮肤健康分析失败: {e}")
            return {}
    
    def _calculate_uniformity(self, image: np.ndarray, skin_mask: np.ndarray) -> float:
        """计算皮肤均匀性"""
        try:
            # 转换为灰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 应用掩码
            masked_gray = gray * (skin_mask / 255.0)
            
            # 计算标准差（均匀性指标）
            valid_pixels = masked_gray[skin_mask > 0]
            if len(valid_pixels) == 0:
                return 0.0
            
            std_dev = np.std(valid_pixels)
            mean_val = np.mean(valid_pixels)
            
            # 均匀性 = 1 - (标准差 / 均值)
            uniformity = max(0, 1 - (std_dev / (mean_val + 1e-6)))
            
            return float(uniformity)
            
        except Exception as e:
            print(f"❌ 均匀性计算失败: {e}")
            return 0.0
    
    def get_detection_confidence(self, image: np.ndarray) -> float:
        """获取检测置信度"""
        try:
            skin_mask = self.detect(image)
            
            # 基于皮肤区域大小和连续性计算置信度
            total_pixels = image.shape[0] * image.shape[1]
            skin_pixels = np.sum(skin_mask > 0)
            
            if total_pixels == 0:
                return 0.0
            
            coverage_ratio = skin_pixels / total_pixels
            
            # 计算连通性
            num_labels, labels = cv2.connectedComponents(skin_mask)
            connectivity_score = 1.0 / (num_labels + 1)  # 连通区域越少越好
            
            # 综合置信度
            confidence = (coverage_ratio * 0.6 + connectivity_score * 0.4)
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            print(f"❌ 置信度计算失败: {e}")
            return 0.5 