#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCM Knowledge Graph Framework Usage Example
传统中医知识图谱框架使用示例

This script demonstrates how to use the TCM Knowledge Graph framework
for health analysis and traditional Chinese medicine recommendations.
"""

import pandas as pd
import json
import os
from pathlib import Path
import sys

class TCMKnowledgeGraphAnalyzer:
    def __init__(self, project_dir=None):
        if project_dir is None:
            project_dir = Path(__file__).parent / "TCM_knowledge_graph-main"
        
        self.project_dir = Path(project_dir)
        self.merge_result_dir = self.project_dir / "merge_result"
        self.entity_dir = self.merge_result_dir / "entity"
        self.relation_dir = self.merge_result_dir / "relation"
        
        # 加载知识图谱数据
        self.entities = {}
        self.relations = {}
        self.load_knowledge_graph()
    
    def load_knowledge_graph(self):
        """加载知识图谱数据"""
        print("加载TCM知识图谱数据...")
        
        # 加载实体数据
        entity_files = [
            'medicinal_material.csv',
            'disease.csv', 
            'ingredient.csv',
            'gene.csv',
            'mm_symptom.csv',
            'syndrome.csv',
            'prescription.csv',
            'flavour.csv',
            'tropism.csv',
            'properties.csv',
            'toxicity.csv'
        ]
        
        for file_name in entity_files:
            file_path = self.entity_dir / file_name
            if file_path.exists():
                try:
                    df = pd.read_csv(file_path)
                    entity_type = file_name.replace('.csv', '')
                    self.entities[entity_type] = df
                    print(f"✓ 加载实体: {entity_type} ({len(df)} 条记录)")
                except Exception as e:
                    print(f"✗ 加载实体失败 {file_name}: {e}")
        
        # 加载关系数据
        relation_files = [
            'herb2ingredient.csv',
            'herb2flavour.csv',
            'herb2property.csv',
            'herb2tropism.csv',
            'herb2syndrome.csv',
            'ingredient_treat_disease.csv',
            'ingredient_associate_gene.csv',
            'disease2mm_symptom.csv',
            'prescription2medicinal_material.csv'
        ]
        
        for file_name in relation_files:
            file_path = self.relation_dir / file_name
            if file_path.exists():
                try:
                    df = pd.read_csv(file_path)
                    relation_type = file_name.replace('.csv', '')
                    self.relations[relation_type] = df
                    print(f"✓ 加载关系: {relation_type} ({len(df)} 条记录)")
                except Exception as e:
                    print(f"✗ 加载关系失败 {file_name}: {e}")
    
    def search_herb_by_name(self, herb_name):
        """根据名称搜索中药材"""
        if 'medicinal_material' not in self.entities:
            return None
        
        herbs_df = self.entities['medicinal_material']
        # 搜索中文名或英文名
        result = herbs_df[
            herbs_df['chinese_name'].str.contains(herb_name, na=False) |
            herbs_df['english_name'].str.contains(herb_name, na=False, case=False)
        ]
        
        return result
    
    def get_herb_properties(self, herb_id):
        """获取中药材的性味归经"""
        properties = {}
        
        # 获取性味
        if 'herb2flavour' in self.relations:
            flavour_relations = self.relations['herb2flavour']
            herb_flavours = flavour_relations[flavour_relations['herb_id'] == herb_id]
            if not herb_flavours.empty and 'flavour' in self.entities:
                flavour_ids = herb_flavours['flavour_id'].tolist()
                flavours = self.entities['flavour'][
                    self.entities['flavour']['flavour_id'].isin(flavour_ids)
                ]['flavour_name'].tolist()
                properties['flavours'] = flavours
        
        # 获取性质
        if 'herb2property' in self.relations:
            property_relations = self.relations['herb2property']
            herb_properties = property_relations[property_relations['herb_id'] == herb_id]
            if not herb_properties.empty and 'properties' in self.entities:
                property_ids = herb_properties['property_id'].tolist()
                props = self.entities['properties'][
                    self.entities['properties']['property_id'].isin(property_ids)
                ]['property_name'].tolist()
                properties['properties'] = props
        
        # 获取归经
        if 'herb2tropism' in self.relations:
            tropism_relations = self.relations['herb2tropism']
            herb_tropisms = tropism_relations[tropism_relations['herb_id'] == herb_id]
            if not herb_tropisms.empty and 'tropism' in self.entities:
                tropism_ids = herb_tropisms['tropism_id'].tolist()
                tropisms = self.entities['tropism'][
                    self.entities['tropism']['tropism_id'].isin(tropism_ids)
                ]['tropism_name'].tolist()
                properties['tropisms'] = tropisms
        
        return properties
    
    def get_herb_ingredients(self, herb_id):
        """获取中药材的化学成分"""
        if 'herb2ingredient' not in self.relations:
            return []
        
        ingredient_relations = self.relations['herb2ingredient']
        herb_ingredients = ingredient_relations[ingredient_relations['herb_id'] == herb_id]
        
        if herb_ingredients.empty or 'ingredient' not in self.entities:
            return []
        
        ingredient_ids = herb_ingredients['ingredient_id'].tolist()
        ingredients = self.entities['ingredient'][
            self.entities['ingredient']['ingredient_id'].isin(ingredient_ids)
        ][['ingredient_name', 'molecular_formula']].to_dict('records')
        
        return ingredients
    
    def find_herbs_for_disease(self, disease_name):
        """查找治疗特定疾病的中药材"""
        # 首先找到疾病
        if 'disease' not in self.entities:
            return []
        
        diseases_df = self.entities['disease']
        disease_result = diseases_df[
            diseases_df['disease_name'].str.contains(disease_name, na=False, case=False)
        ]
        
        if disease_result.empty:
            return []
        
        disease_id = disease_result.iloc[0]['disease_id']
        
        # 通过成分-疾病关系找到相关成分
        if 'ingredient_treat_disease' not in self.relations:
            return []
        
        ingredient_disease_relations = self.relations['ingredient_treat_disease']
        related_ingredients = ingredient_disease_relations[
            ingredient_disease_relations['disease_id'] == disease_id
        ]
        
        if related_ingredients.empty:
            return []
        
        ingredient_ids = related_ingredients['ingredient_id'].tolist()
        
        # 通过成分-中药关系找到相关中药
        if 'herb2ingredient' not in self.relations:
            return []
        
        herb_ingredient_relations = self.relations['herb2ingredient']
        related_herbs = herb_ingredient_relations[
            herb_ingredient_relations['ingredient_id'].isin(ingredient_ids)
        ]
        
        if related_herbs.empty or 'medicinal_material' not in self.entities:
            return []
        
        herb_ids = related_herbs['herb_id'].unique().tolist()
        herbs = self.entities['medicinal_material'][
            self.entities['medicinal_material']['herb_id'].isin(herb_ids)
        ][['chinese_name', 'english_name', 'herb_id']].to_dict('records')
        
        return herbs
    
    def analyze_prescription(self, prescription_name):
        """分析处方组成"""
        if 'prescription' not in self.entities:
            return None
        
        prescriptions_df = self.entities['prescription']
        prescription_result = prescriptions_df[
            prescriptions_df['prescription_name'].str.contains(prescription_name, na=False)
        ]
        
        if prescription_result.empty:
            return None
        
        prescription_id = prescription_result.iloc[0]['prescription_id']
        
        # 获取处方中的中药材
        if 'prescription2medicinal_material' not in self.relations:
            return None
        
        prescription_herbs = self.relations['prescription2medicinal_material']
        herbs_in_prescription = prescription_herbs[
            prescription_herbs['prescription_id'] == prescription_id
        ]
        
        if herbs_in_prescription.empty or 'medicinal_material' not in self.entities:
            return None
        
        herb_ids = herbs_in_prescription['herb_id'].tolist()
        herbs = self.entities['medicinal_material'][
            self.entities['medicinal_material']['herb_id'].isin(herb_ids)
        ][['chinese_name', 'english_name']].to_dict('records')
        
        return {
            'prescription_info': prescription_result.iloc[0].to_dict(),
            'herbs': herbs,
            'herb_count': len(herbs)
        }
    
    def get_statistics(self):
        """获取知识图谱统计信息"""
        stats = {
            'entities': {},
            'relations': {},
            'total_entities': 0,
            'total_relations': 0
        }
        
        for entity_type, df in self.entities.items():
            count = len(df)
            stats['entities'][entity_type] = count
            stats['total_entities'] += count
        
        for relation_type, df in self.relations.items():
            count = len(df)
            stats['relations'][relation_type] = count
            stats['total_relations'] += count
        
        return stats

def main():
    """主函数 - 演示TCM知识图谱的使用"""
    print("TCM Knowledge Graph Framework 使用示例")
    print("="*50)
    
    # 初始化分析器
    analyzer = TCMKnowledgeGraphAnalyzer()
    
    # 显示统计信息
    print("\n1. 知识图谱统计信息:")
    stats = analyzer.get_statistics()
    print(f"   总实体数: {stats['total_entities']}")
    print(f"   总关系数: {stats['total_relations']}")
    
    print("\n   实体类型分布:")
    for entity_type, count in stats['entities'].items():
        print(f"   - {entity_type}: {count}")
    
    # 示例1: 搜索中药材
    print("\n2. 搜索中药材示例:")
    herb_name = "人参"
    herbs = analyzer.search_herb_by_name(herb_name)
    if herbs is not None and not herbs.empty:
        print(f"   搜索 '{herb_name}' 的结果:")
        for _, herb in herbs.head(3).iterrows():
            print(f"   - {herb.get('chinese_name', 'N/A')} ({herb.get('english_name', 'N/A')})")
            
            # 获取性味归经
            herb_id = herb.get('herb_id')
            if herb_id:
                properties = analyzer.get_herb_properties(herb_id)
                if properties:
                    print(f"     性味: {', '.join(properties.get('flavours', []))}")
                    print(f"     性质: {', '.join(properties.get('properties', []))}")
                    print(f"     归经: {', '.join(properties.get('tropisms', []))}")
    
    # 示例2: 查找治疗疾病的中药
    print("\n3. 查找治疗疾病的中药示例:")
    disease_name = "感冒"
    herbs_for_disease = analyzer.find_herbs_for_disease(disease_name)
    if herbs_for_disease:
        print(f"   治疗 '{disease_name}' 的中药材:")
        for herb in herbs_for_disease[:5]:
            print(f"   - {herb.get('chinese_name', 'N/A')} ({herb.get('english_name', 'N/A')})")
    
    # 示例3: 分析处方
    print("\n4. 处方分析示例:")
    prescription_name = "麻黄汤"
    prescription_analysis = analyzer.analyze_prescription(prescription_name)
    if prescription_analysis:
        print(f"   处方: {prescription_analysis['prescription_info'].get('prescription_name', 'N/A')}")
        print(f"   组成药材数: {prescription_analysis['herb_count']}")
        print("   药材组成:")
        for herb in prescription_analysis['herbs']:
            print(f"   - {herb.get('chinese_name', 'N/A')} ({herb.get('english_name', 'N/A')})")
    
    print("\n✓ TCM Knowledge Graph Framework 示例演示完成!")

if __name__ == "__main__":
    main()
