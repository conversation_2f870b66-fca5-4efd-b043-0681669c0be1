@echo off
chcp 65001 >nul
title 传统健康智慧时钟 v1.1.2 完整功能版 - 智能启动器
echo.
echo 🏥 传统健康智慧时钟 v1.1.2 完整功能版
echo 🎯 完整功能修复：时钟主界面 + 人脸跟踪框 + 综合报告
echo.
echo 📋 修复内容：
echo    • ✅ 时钟主界面完整集成
echo    • ✅ 人脸检测跟踪框正常显示
echo    • ✅ 综合健康报告功能完整
echo    • ✅ OpenCV中文路径编码问题完全解决
echo    • ✅ 文件大小保持516MB（完整功能）
echo.
echo 🚀 正在启动完整功能版程序...
echo.

REM 智能路径检测和启动
cd /d "%~dp0"

REM 优先使用v1.1.2完整版
if exist "dist\HealthMonitor_v1.1.2_Complete\HealthMonitor_v1.1.2_Complete.exe" (
    echo ✅ 启动v1.1.2完整功能版...
    cd "dist\HealthMonitor_v1.1.2_Complete"
    start "" "HealthMonitor_v1.1.2_Complete.exe"
    echo.
    echo 🎉 程序已启动！所有功能应该正常工作了
    echo 💡 功能测试步骤：
    echo    1. 验证时钟主界面正常显示
    echo    2. 点击"🏥 健康监测"功能
    echo    3. 在摄像头前查看绿色人脸跟踪框
    echo    4. 等待分析完成，查看综合报告
    echo    5. 应该能看到"✅ [跟踪] 显示 X 个人脸框"输出
) else if exist "dist\HealthMonitor_Enhanced\HealthMonitor_v1.1.1_TrackingFixed.exe" (
    echo ✅ 启动v1.1.1版本...
    cd "dist\HealthMonitor_Enhanced"
    start "" "HealthMonitor_v1.1.1_TrackingFixed.exe"
    echo 🎉 已启动v1.1.1版本
) else (
    echo ❌ 未找到可执行文件
    echo 💡 请重新编译程序
)

echo.
echo 📖 说明：
echo    v1.1.2版本完整解决了所有编译问题：
echo    • 时钟主界面与健康监测完美集成
echo    • 人脸跟踪框在编译版本中正常显示
echo    • 综合健康报告功能完整可用
echo    • 文件大小约516M，包含所有必要依赖
echo.
pause
