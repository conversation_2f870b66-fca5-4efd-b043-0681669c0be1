#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传统健康智慧时钟启动器 v1.1.2 - 完整功能版智能启动器
"""

import os
import sys
import subprocess

def main():
    print("🏥 传统健康智慧时钟 v1.1.2 完整功能版")
    print("🔧 智能启动器正在检测最佳运行方式...")

    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 检查v1.1.2完整版
    complete_exe = os.path.join(current_dir, "dist", "HealthMonitor_v1.1.2_Complete", "HealthMonitor_v1.1.2_Complete.exe")
    if os.path.exists(complete_exe):
        print("✅ 启动v1.1.2完整功能版...")
        subprocess.Popen([complete_exe])
        return

    # 检查v1.1.1增强版
    enhanced_exe = os.path.join(current_dir, "dist", "HealthMonitor_Enhanced", "HealthMonitor_v1.1.1_TrackingFixed.exe")
    if os.path.exists(enhanced_exe):
        print("✅ 启动v1.1.1增强版...")
        subprocess.Popen([enhanced_exe])
        return

    # 回退到Python版本
    python_main = os.path.join(current_dir, "bagua_clock.py")
    if os.path.exists(python_main):
        print("✅ 启动Python版本...")
        subprocess.Popen([sys.executable, python_main])
        return

    print("❌ 未找到可用版本")

if __name__ == "__main__":
    main()
