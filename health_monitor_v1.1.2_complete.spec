# -*- mode: python ; coding: utf-8 -*-
# 传统健康智慧时钟 v1.1.2 完整功能版 - PyInstaller编译配置
# 🎯 修复目标：时钟主界面 + 人脸跟踪框 + 综合报告 + 500M文件大小

import sys
import os
import cv2

# 🔧 动态检测路径
python_path = sys.executable
python_dir = os.path.dirname(python_path)
site_packages = os.path.join(python_dir, "Lib", "site-packages")
cv2_data_path = os.path.join(site_packages, "cv2", "data")

print(f"🔧 [Spec] Python路径: {python_path}")
print(f"🔧 [Spec] OpenCV数据路径: {cv2_data_path}")

block_cipher = None

# 🔥 分析脚本依赖 - v1.1.2完整功能版
a = Analysis(
    ['bagua_clock.py'],  # 主程序文件
    pathex=[],
    binaries=[],
    datas=[
        # 🔥 关键修复：OpenCV数据文件 - 动态路径检测
        (cv2_data_path, 'cv2/data/'),
        
        # 🔥 健康监测模块文件 - 确保完整包含
        ('advanced_health_monitor.py', '.'),
        
        # 🔥 配置文件 - 完整配置系统
        ('advanced_health_settings.json', '.'),
        ('ai_health_analysis_records.json', '.'),
        ('ai_health_settings.json', '.'),
        ('system_settings.json', '.'),
        ('alarms.json', '.'),
        ('notepad_password.json', '.'),
        ('water_intake_records.json', '.'),
    ],
    hiddenimports=[
        # 🔥 主要模块 - 确保完整导入
        'bagua_clock',
        'advanced_health_monitor',
        
        # 🔥 PyQt5相关 - 完整GUI支持
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtPrintSupport',  # 报告打印支持
        'PyQt5.sip',
        
        # 🔥 OpenCV相关 - 关键修复人脸检测
        'cv2',
        'cv2.data',
        'cv2.face',  # 人脸识别模块
        'cv2.objdetect',  # 目标检测模块
        
        # 🔥 数据处理 - 完整numpy支持
        'numpy',
        'numpy.core',
        'numpy.core.multiarray',
        'numpy.random',
        'numpy.linalg',
        'numpy.fft',  # 频域分析
        
        # 🔥 图像处理 - 完整PIL支持
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageDraw',
        'PIL.ImageFont',  # 字体支持
        'PIL.ImageFilter',  # 滤镜支持
        
        # 🔥 科学计算 - 完整scipy支持
        'scipy',
        'scipy.spatial',
        'scipy.spatial.distance',
        'scipy.ndimage',  # 图像处理
        'scipy.signal',   # 信号处理
        
        # 🔥 日期时间 - 完整时间支持
        'datetime',
        'calendar',
        'time',
        
        # 🔥 系统相关 - 完整系统支持
        'threading',
        'queue',
        'json',
        'configparser',
        'sqlite3',
        'os',
        'sys',
        'gc',  # 垃圾回收
        
        # 🔥 AI相关 - 完整AI库支持
        'mediapipe',
        'mediapipe.solutions',
        'mediapipe.solutions.face_mesh',
        'mediapipe.solutions.face_detection',
        'tensorflow',
        'tensorflow.keras',
        'dlib',
        'face_recognition',
        'face_recognition_models',  # 人脸识别模型
        
        # 🔥 网络和音频
        'pygame',
        'pygame.mixer',  # 音频支持
        'psutil',
        'requests',
        'urllib3',  # 网络请求
        
        # 🔥 数学和统计
        'math',
        'statistics',
        'random',
        
        # 🔥 并发处理
        'concurrent.futures',
        'multiprocessing',
        
        # 🔥 文件处理
        'pathlib',
        'shutil',
        'tempfile',
        
        # 🔥 lunar_python农历库
        'lunar_python',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 🔧 优化：排除不需要的大型库，但保留核心功能
        'matplotlib.pyplot',  # 排除绘图库
        'tkinter',           # 排除tkinter GUI
        'IPython',           # 排除交互式Python
        'jupyter',           # 排除Jupyter
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 🔥 处理二进制文件 - 优化配置
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 🔥 创建可执行文件 - v1.1.2完整功能版配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='HealthMonitor_v1.1.2_Complete',  # 🔧 更新版本号
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 🔧 启用UPX压缩保持500M大小
    upx_exclude=[
        # 🔧 排除某些文件避免压缩问题
        'vcruntime140.dll',
        'python311.dll',
        'opencv_world*.dll',
    ],
    runtime_tmpdir=None,
    console=False,  # 🔧 隐藏控制台窗口
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 🔧 可以添加图标
    version_info={
        'version': '*******',
        'description': '传统健康智慧时钟 - 完整功能版',
        'product_name': 'Traditional Health Smart Clock Complete',
        'file_description': 'Complete Health Monitor with Face Tracking & Reports',
        'copyright': '2025 湖南全航信息通信有限公司',
        'company_name': '湖南全航信息通信有限公司'
    }
)
