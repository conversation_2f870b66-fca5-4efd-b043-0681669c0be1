#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SkinDetector集成测试脚本
"""

import sys
import traceback

def test_skindetector_module():
    """测试SkinDetector模块"""
    print("🧪 测试SkinDetector模块...")
    try:
        from skin_detector_integration import EnhancedSkinDetector, SkinDetectorAdapter
        print("✅ SkinDetector模块导入成功")
        return True
    except Exception as e:
        print(f"❌ SkinDetector模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_health_monitor_integration():
    """测试健康监测系统集成"""
    print("🧪 测试健康监测系统集成...")
    try:
        from advanced_health_monitor import AdvancedHealthAnalyzer
        print("✅ 健康监测系统导入成功")
        
        # 检查是否有SkinDetector相关代码
        with open('advanced_health_monitor.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'SkinDetector' in content:
                print("✅ 健康监测系统包含SkinDetector集成代码")
                return True
            else:
                print("❌ 健康监测系统未包含SkinDetector集成代码")
                return False
                
    except Exception as e:
        print(f"❌ 健康监测系统集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_import_skindetector():
    """测试SkinDetector库"""
    print("🧪 测试SkinDetector库...")
    try:
        from skindetector import SkinDetector
        print("✅ SkinDetector库可用")
        return True
    except ImportError:
        print("⚠️ SkinDetector库未安装，将使用备用方法")
        return False
    except Exception as e:
        print(f"❌ SkinDetector库测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 SkinDetector集成测试开始")
    print("=" * 50)
    
    tests = [
        ("SkinDetector库", test_import_skindetector),
        ("SkinDetector模块", test_skindetector_module),
        ("健康监测系统集成", test_health_monitor_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！SkinDetector集成成功！")
        return True
    elif passed >= total // 2:
        print("⚠️ 部分测试通过，SkinDetector基本可用")
        return True
    else:
        print("❌ 大部分测试失败，需要检查安装和配置")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ SkinDetector集成测试成功！")
    else:
        print("\n❌ SkinDetector集成测试失败")
    
    input("\n按回车键退出...") 