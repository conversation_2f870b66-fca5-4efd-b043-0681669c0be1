#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCM健康监测系统集成测试
Test TCM Health Monitoring System Integration
"""

import sys
import os
import cv2
import numpy as np
from datetime import datetime
import traceback

def test_tcm_integration():
    """测试TCM集成功能"""
    print("🧪 [测试] 开始TCM健康监测系统集成测试...")
    
    try:
        # 1. 测试TCM集成模块导入
        print("\n1️⃣ 测试TCM集成模块导入...")
        from tcm_ai_integration import TCMHealthIntegration, get_tcm_integration
        print("✅ TCM集成模块导入成功")
        
        # 2. 测试健康监测系统导入
        print("\n2️⃣ 测试健康监测系统导入...")
        from advanced_health_monitor import AdvancedHealthMonitorWidget
        print("✅ 健康监测系统导入成功")
        
        # 3. 创建测试数据
        print("\n3️⃣ 创建测试数据...")
        test_face_analysis = {
            "face_detected": True,
            "skin_health_score": 72,
            "health_score": 68,
            "brightness": 135,
            "saturation": 95,
            "uniformity": 0.75,
            "texture_quality": 78,
            "face_color_analysis": {
                "overall_status": "正常",
                "tcm_color_type": "微黄",
                "brightness": 135
            },
            "timestamp": datetime.now().isoformat()
        }
        print("✅ 测试数据创建完成")
        
        # 4. 测试TCM增强分析
        print("\n4️⃣ 测试TCM增强分析...")
        tcm_integration = get_tcm_integration()
        enhanced_result = tcm_integration.enhance_health_analysis(test_face_analysis)
        print("✅ TCM增强分析完成")
        
        # 5. 验证增强结果
        print("\n5️⃣ 验证增强结果...")
        if "tcm_analysis" in enhanced_result:
            tcm_result = enhanced_result["tcm_analysis"]
            print(f"✅ TCM分析结果已添加")
            print(f"   - 主要证候: {tcm_result['syndrome_analysis']['primary_syndrome']}")
            print(f"   - 体质类型: {tcm_result['constitution_type']}")
            print(f"   - 置信度: {tcm_result['confidence_score']:.2f}")
            
            # 检查各个分析组件
            if "tcm_diagnosis" in tcm_result:
                print(f"   - 面诊结果: ✅")
            if "herb_recommendations" in tcm_result:
                print(f"   - 中药推荐: ✅ ({len(tcm_result['herb_recommendations'])}个)")
            if "health_suggestions" in tcm_result:
                print(f"   - 健康建议: ✅ ({len(tcm_result['health_suggestions'])}条)")
        else:
            print("❌ TCM分析结果未找到")
            return False
        
        # 6. 测试知识图谱状态
        print("\n6️⃣ 测试知识图谱状态...")
        status = tcm_integration.get_integration_status()
        print(f"✅ 集成状态获取成功")
        print(f"   - 集成启用: {status['integration_enabled']}")
        print(f"   - 知识图谱加载: {status['tcm_analyzer_status']['knowledge_loaded']}")
        print(f"   - 实体类型数: {status['tcm_analyzer_status']['entities_count']}")
        
        # 7. 测试模拟摄像头数据
        print("\n7️⃣ 测试模拟摄像头数据...")
        # 创建模拟的面部图像
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        # 绘制一个简单的面部区域（肤色）
        cv2.rectangle(test_frame, (200, 150), (440, 350), (220, 180, 150), -1)  # 肤色矩形
        cv2.circle(test_frame, (280, 200), 15, (50, 50, 50), -1)  # 左眼
        cv2.circle(test_frame, (360, 200), 15, (50, 50, 50), -1)  # 右眼
        cv2.ellipse(test_frame, (320, 280), (30, 15), 0, 0, 180, (180, 120, 120), -1)  # 嘴巴
        
        print("✅ 模拟面部图像创建完成")
        
        # 8. 测试完整的分析流程
        print("\n8️⃣ 测试完整分析流程...")
        
        # 模拟健康监测系统的分析结果
        mock_analysis_result = {
            "analysis_id": "test_analysis_001",
            "face_detected": True,
            "analysis_status": "分析完成",
            "face_analysis": enhanced_result,
            "emotion_analysis": {"primary_emotion": "neutral", "confidence": 0.8},
            "health_alerts": []
        }
        
        print("✅ 完整分析流程测试完成")
        
        # 9. 生成测试报告
        print("\n9️⃣ 生成测试报告...")
        test_report = generate_test_report(enhanced_result)
        print("✅ 测试报告生成完成")
        
        # 保存测试报告
        with open("tcm_integration_test_report.txt", "w", encoding="utf-8") as f:
            f.write(test_report)
        print("✅ 测试报告已保存到 tcm_integration_test_report.txt")
        
        print("\n🎉 [测试完成] TCM健康监测系统集成测试全部通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ [测试失败] TCM集成测试出现错误: {e}")
        traceback.print_exc()
        return False

def generate_test_report(enhanced_result):
    """生成测试报告"""
    report = f"""
📋 TCM健康监测系统集成测试报告
生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
{'=' * 60}

🎯 测试概述：
本次测试验证了TCM Knowledge Graph Framework与健康监测系统的集成效果。

📊 测试结果：
"""
    
    if "tcm_analysis" in enhanced_result:
        tcm_data = enhanced_result["tcm_analysis"]
        
        report += f"""
🏥 TCM分析结果：
• 主要证候: {tcm_data['syndrome_analysis']['primary_syndrome']}
• 体质类型: {tcm_data['constitution_type']}
• 分析置信度: {tcm_data['confidence_score']:.2f}

🔍 面诊详情：
"""
        
        # 面诊结果
        if "tcm_diagnosis" in tcm_data:
            diagnosis = tcm_data["tcm_diagnosis"]
            if "face_color" in diagnosis:
                face_color = diagnosis["face_color"]
                report += f"• 面色类型: {face_color.get('color_type', '正常')}\n"
                report += f"• 对应证候: {face_color.get('syndrome', '平和证')}\n"
        
        # 中药推荐
        if "herb_recommendations" in tcm_data and tcm_data["herb_recommendations"]:
            report += f"\n🌿 中药推荐：\n"
            for i, herb in enumerate(tcm_data["herb_recommendations"][:3], 1):
                name = herb.get("name", "未知")
                property_info = herb.get("property", "")
                report += f"{i}. {name} ({property_info})\n"
        
        # 健康建议
        if "health_suggestions" in tcm_data and tcm_data["health_suggestions"]:
            report += f"\n💡 健康建议：\n"
            for i, suggestion in enumerate(tcm_data["health_suggestions"][:3], 1):
                report += f"{i}. {suggestion}\n"
    
    report += f"""

✅ 集成状态：
• TCM模块加载: 成功
• 知识图谱数据: 已加载
• 分析功能: 正常运行
• 报告生成: 正常

🎯 结论：
TCM Knowledge Graph Framework已成功集成到健康监测系统中，
能够提供基于传统中医理论的面部分析和健康建议。

📝 技术特点：
1. 基于真实面部检测数据进行中医分析
2. 结合现代AI技术与传统中医理论
3. 提供个性化的中药推荐和养生建议
4. 支持多维度的体质分析和证候判断

{'=' * 60}
测试完成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
"""
    
    return report

def test_camera_integration():
    """测试摄像头集成"""
    print("\n🎥 [摄像头测试] 测试摄像头集成...")
    
    try:
        # 尝试打开摄像头
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("⚠️ 摄像头无法打开，跳过摄像头测试")
            return True
        
        print("✅ 摄像头打开成功")
        
        # 读取一帧
        ret, frame = cap.read()
        if ret:
            print("✅ 摄像头数据读取成功")
            print(f"   - 图像尺寸: {frame.shape}")
            
            # 保存测试图像
            cv2.imwrite("test_camera_frame.jpg", frame)
            print("✅ 测试图像已保存")
        else:
            print("⚠️ 摄像头数据读取失败")
        
        cap.release()
        print("✅ 摄像头资源释放完成")
        return True
        
    except Exception as e:
        print(f"❌ 摄像头测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 启动TCM健康监测系统集成测试")
    print("=" * 50)
    
    # 运行主要测试
    success = test_tcm_integration()
    
    # 运行摄像头测试
    camera_success = test_camera_integration()
    
    print("\n" + "=" * 50)
    if success and camera_success:
        print("🎉 所有测试通过！TCM集成系统准备就绪。")
        print("\n📋 测试报告已生成：tcm_integration_test_report.txt")
        print("🎥 如果有摄像头，测试图像已保存：test_camera_frame.jpg")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    
    print("\n💡 提示：现在可以运行主程序测试完整的TCM健康监测功能。")
