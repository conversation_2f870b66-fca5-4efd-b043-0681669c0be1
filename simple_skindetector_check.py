#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单SkinDetector检查
"""

import sys
import os

def check_skindetector_integration():
    """检查SkinDetector集成状态"""
    print("🔍 检查SkinDetector集成状态")
    print("=" * 40)
    
    # 1. 检查模块导入
    print("📋 检查1: 模块导入")
    try:
        from skindetector import SkinDetector
        print("✅ skindetector模块导入成功")
    except ImportError as e:
        print(f"❌ skindetector模块导入失败: {e}")
        return False
    
    try:
        from skin_detector_integration import SkinDetectorAdapter, EnhancedSkinDetector
        print("✅ skin_detector_integration模块导入成功")
    except ImportError as e:
        print(f"❌ skin_detector_integration模块导入失败: {e}")
        return False
    
    # 2. 检查主程序中的集成标志
    print("\n📋 检查2: 主程序集成标志")
    try:
        import advanced_health_monitor
        print(f"✅ 主程序导入成功")
        print(f"🔧 SKINDETECTOR_INTEGRATION_AVAILABLE: {advanced_health_monitor.SKINDETECTOR_INTEGRATION_AVAILABLE}")
    except Exception as e:
        print(f"❌ 主程序导入失败: {e}")
        return False
    
    # 3. 测试SkinDetector实例化
    print("\n📋 检查3: SkinDetector实例化")
    try:
        detector = SkinDetector()
        print("✅ SkinDetector实例化成功")
    except Exception as e:
        print(f"❌ SkinDetector实例化失败: {e}")
        return False
    
    # 4. 测试适配器实例化
    print("\n📋 检查4: 适配器实例化")
    try:
        class MockMonitor:
            def __init__(self):
                self.name = "MockMonitor"
        
        mock_monitor = MockMonitor()
        adapter = SkinDetectorAdapter(mock_monitor)
        print("✅ 适配器实例化成功")
    except Exception as e:
        print(f"❌ 适配器实例化失败: {e}")
        return False
    
    # 5. 检查主程序中的适配器初始化
    print("\n📋 检查5: 主程序适配器初始化")
    try:
        # 模拟主程序的初始化过程
        if advanced_health_monitor.SKINDETECTOR_INTEGRATION_AVAILABLE:
            print("✅ 集成标志为True，应该能初始化适配器")
        else:
            print("❌ 集成标志为False，适配器不会被初始化")
            return False
    except Exception as e:
        print(f"❌ 检查集成标志失败: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("✅ 所有检查通过！SkinDetector集成应该正常工作")
    return True

if __name__ == "__main__":
    success = check_skindetector_integration()
    if success:
        print("\n🎉 SkinDetector集成检查成功！")
    else:
        print("\n❌ SkinDetector集成检查失败！") 