#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SkinDetector是否真正参与皮肤健康计算
"""

import cv2
import numpy as np

def test_skindetector_calculation():
    """测试SkinDetector是否真正参与计算"""
    print("🧪 测试SkinDetector计算参与度...")
    
    try:
        # 1. 测试SkinDetector库
        from skindetector import SkinDetector
        detector = SkinDetector()
        print("✅ SkinDetector库可用")
        
        # 2. 测试集成模块
        from skin_detector_integration import EnhancedSkinDetector, SkinDetectorAdapter
        print("✅ 集成模块可用")
        
        # 3. 创建测试图像
        test_image = np.zeros((300, 300, 3), dtype=np.uint8)
        # 模拟不同肤色
        test_image[50:250, 50:250] = [255, 200, 150]  # 浅肤色
        test_image[100:200, 100:200] = [200, 150, 100]  # 深肤色
        
        # 4. 测试直接SkinDetector检测
        print("\n📋 测试直接SkinDetector检测...")
        mask = detector.detect(test_image)
        health_analysis = detector.analyze_skin_health(test_image)
        confidence = detector.get_detection_confidence(test_image)
        
        print(f"✅ 直接检测结果:")
        print(f"  - 掩码形状: {mask.shape}")
        print(f"  - 皮肤覆盖率: {health_analysis.get('skin_coverage', 0):.3f}")
        print(f"  - 平均亮度: {health_analysis.get('brightness', 0):.1f}")
        print(f"  - 饱和度: {health_analysis.get('saturation', 0):.1f}")
        print(f"  - 均匀性: {health_analysis.get('uniformity', 0):.3f}")
        print(f"  - 检测置信度: {confidence:.3f}")
        
        # 5. 测试增强检测器
        print("\n📋 测试增强检测器...")
        enhanced_detector = EnhancedSkinDetector()
        enhanced_result = enhanced_detector.detect_skin_regions(test_image)
        
        print(f"✅ 增强检测结果:")
        print(f"  - 检测方法: {enhanced_result.get('detection_method', 'unknown')}")
        print(f"  - 检测区域数: {len(enhanced_result.get('skin_regions', []))}")
        print(f"  - 检测置信度: {enhanced_result.get('confidence', 0):.3f}")
        
        # 6. 测试适配器增强
        print("\n📋 测试适配器增强...")
        class MockHealthMonitor:
            def __init__(self):
                self.name = "MockHealthMonitor"
        
        mock_monitor = MockHealthMonitor()
        adapter = SkinDetectorAdapter(mock_monitor)
        
        existing_analysis = {
            "skin_health_score": 75.0,
            "skin_uniformity": 0.8,
            "texture_quality": 85.0,
            "brightness_score": 0.7
        }
        
        enhanced_analysis = adapter.enhance_skin_analysis(test_image, existing_analysis)
        
        print(f"✅ 适配器增强结果:")
        print(f"  - 是否增强: {enhanced_analysis.get('skindetector_enhanced', False)}")
        print(f"  - 原评分: {existing_analysis.get('skin_health_score', 0):.1f}")
        print(f"  - 新评分: {enhanced_analysis.get('skin_health_score', 0):.1f}")
        
        if enhanced_analysis.get('enhanced_skin_health_score'):
            print(f"  - 增强评分: {enhanced_analysis.get('enhanced_skin_health_score', 0):.1f}")
        
        # 7. 检查是否有实际改进
        original_score = existing_analysis.get('skin_health_score', 0)
        new_score = enhanced_analysis.get('skin_health_score', 0)
        
        if abs(new_score - original_score) > 0.1:
            print(f"✅ SkinDetector确实参与了计算！评分变化: {original_score:.1f} -> {new_score:.1f}")
            return True
        else:
            print(f"⚠️ SkinDetector可能没有显著影响评分: {original_score:.1f} -> {new_score:.1f}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_health_monitor_integration():
    """测试健康监测系统集成效果"""
    print("\n🧪 测试健康监测系统集成效果...")
    
    try:
        # 模拟健康监测系统的皮肤分析
        from advanced_health_monitor import AdvancedHealthAnalyzer
        
        # 创建分析器（这会初始化SkinDetector适配器）
        analyzer = AdvancedHealthAnalyzer()
        
        # 检查适配器状态
        if hasattr(analyzer, 'skin_detector_adapter'):
            adapter_status = analyzer.skin_detector_adapter is not None
            print(f"✅ 健康监测系统适配器状态: {adapter_status}")
            
            if adapter_status:
                # 获取集成状态
                status = analyzer.skin_detector_adapter.get_integration_status()
                print(f"✅ 集成状态: {status}")
                return True
            else:
                print("❌ 适配器未初始化")
                return False
        else:
            print("❌ 健康监测系统没有适配器属性")
            return False
            
    except Exception as e:
        print(f"❌ 健康监测系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 SkinDetector计算参与度测试")
    print("=" * 50)
    
    # 测试SkinDetector计算
    calculation_ok = test_skindetector_calculation()
    
    # 测试健康监测系统集成
    integration_ok = test_health_monitor_integration()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"SkinDetector计算参与: {'✅ 是' if calculation_ok else '❌ 否'}")
    print(f"健康监测系统集成: {'✅ 正常' if integration_ok else '❌ 异常'}")
    
    if calculation_ok and integration_ok:
        print("\n🎉 SkinDetector已成功参与皮肤健康计算！")
        print("现在启动健康监测系统时，您应该能看到增强的分析结果。")
    elif calculation_ok:
        print("\n⚠️ SkinDetector计算正常，但健康监测系统集成可能有问题")
    else:
        print("\n❌ SkinDetector可能没有正确参与计算")

if __name__ == "__main__":
    main() 