#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制启用SkinDetector测试
"""

import cv2
import numpy as np
import sys
import os

def force_skindetector_test():
    """强制测试SkinDetector"""
    print("🚀 强制SkinDetector测试开始")
    print("=" * 50)
    
    # 1. 检查当前目录
    print(f"📁 当前目录: {os.getcwd()}")
    
    # 2. 检查文件是否存在
    skindetector_files = [
        "skindetector/__init__.py",
        "skindetector/skindetector.py",
        "skin_detector_integration.py"
    ]
    
    for file_path in skindetector_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} 存在 ({size} bytes)")
        else:
            print(f"❌ {file_path} 不存在")
    
    # 3. 强制导入测试
    print("\n🧪 强制导入测试...")
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.getcwd())
        print("✅ 已添加当前目录到Python路径")
        
        # 测试SkinDetector库
        try:
            from skindetector import SkinDetector
            print("✅ SkinDetector库导入成功")
            
            # 测试创建实例
            detector = SkinDetector()
            print("✅ SkinDetector实例创建成功")
            
            # 测试基本功能
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            test_image[:, :] = [255, 200, 150]
            
            mask = detector.detect(test_image)
            print(f"✅ 检测功能正常，掩码形状: {mask.shape}")
            
        except Exception as e:
            print(f"❌ SkinDetector库测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试集成模块
        try:
            from skin_detector_integration import EnhancedSkinDetector, SkinDetectorAdapter
            print("✅ 集成模块导入成功")
            
            # 测试增强检测器
            enhanced = EnhancedSkinDetector()
            print("✅ 增强检测器创建成功")
            
            # 测试检测
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            test_image[:, :] = [255, 200, 150]
            
            result = enhanced.detect_skin_regions(test_image)
            print(f"✅ 增强检测成功: {result.get('detection_method', 'unknown')}")
            
        except Exception as e:
            print(f"❌ 集成模块测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试适配器
        try:
            class MockHealthMonitor:
                def __init__(self):
                    self.name = "MockHealthMonitor"
            
            mock_monitor = MockHealthMonitor()
            adapter = SkinDetectorAdapter(mock_monitor)
            print("✅ 适配器创建成功")
            
            # 测试增强分析
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            test_image[:, :] = [255, 200, 150]
            
            existing_analysis = {"skin_health_score": 75.0}
            enhanced_result = adapter.enhance_skin_analysis(test_image, existing_analysis)
            
            print(f"✅ 增强分析成功: {enhanced_result.get('skindetector_enhanced', False)}")
            
        except Exception as e:
            print(f"❌ 适配器测试失败: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 整体测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")

if __name__ == "__main__":
    force_skindetector_test() 