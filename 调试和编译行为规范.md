# 传统健康智慧时钟 - 调试和编译行为规范

## 🎯 项目概述

传统健康智慧时钟是一个集成AI健康监测、传统中医理论和现代时钟功能的综合性应用程序。本文档规范了项目的调试和编译流程，确保开发过程的标准化和高效性。

## 📋 开发环境规范

### ✅ 基础环境要求
- **操作系统**: Windows 10/11 (64位)
- **Python版本**: Python 3.11.9 (强制要求，确保兼容性)
- **IDE推荐**: PyCharm Professional / VS Code / Cursor
- **Git版本**: 2.40+
- **内存要求**: 最低8GB，推荐16GB+

### ✅ Python依赖包清单
```bash
# 核心框架
PyQt5==5.15.10
pygame==2.5.2

# AI和图像处理
opencv-python==********
numpy==1.24.3
face-recognition==1.3.0
mediapipe==0.10.9
tensorflow==2.13.0
Pillow==10.0.1

# 编译工具
PyInstaller==6.3.0

# 系统工具
psutil==5.9.6
winsound (Windows内置)
```

### ✅ 开发工具配置
1. **代码格式化**: 使用 Black + isort
2. **类型检查**: mypy (可选)
3. **代码质量**: flake8 + pylint
4. **版本控制**: Git + .gitignore 配置
5. **文档生成**: Sphinx (可选)

## 🔧 编译流程规范

### ✅ 编译前检查清单
1. **环境验证**
   ```bash
   py -3.11 --version  # 确认Python版本
   pip list | findstr "PyQt5\|opencv\|tensorflow"  # 检查关键依赖
   ```

2. **文件完整性检查**
   - `bagua_clock.py` (主程序，约5000行)
   - `advanced_health_monitor.py` (健康监测，约8000行)
   - `传统健康智慧时钟_v1.1.1_跟踪框修复版.spec` (编译配置)
   - 所有配置文件 (*.json)

3. **版本一致性验证**
   ```bash
   # 检查所有文件中的版本号
   findstr /s "v1.1.1" *.py *.spec *.bat
   ```

### ✅ 标准编译流程
```bash
# 1. 清理旧文件
Remove-Item -Path "build" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue

# 2. 执行编译
py -3.11 compile_bagua_clock_full_ai.py

# 3. 验证结果
dir "dist\传统健康智慧时钟_v1.1.1_跟踪框修复版"
```

### ✅ 编译参数说明
- `--clean`: 清理临时文件
- `--noconfirm`: 无需确认，自动执行
- `console=False`: 隐藏控制台窗口
- `upx=True`: 启用UPX压缩

## 🐛 调试规范

### ✅ 调试环境设置
1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **开发模式运行**
   ```bash
   py -3.11 bagua_clock.py --debug
   ```

3. **内存监控**
   ```python
   import psutil
   import gc
   # 定期监控内存使用
   ```

### ✅ 常见问题排查

#### 🔍 启动问题
1. **ImportError**: 检查依赖包安装
2. **ModuleNotFoundError**: 验证PYTHONPATH
3. **DLL错误**: 重新安装相关包

#### 🔍 摄像头问题
1. **无法检测摄像头**
   ```python
   # 测试代码
   import cv2
   for i in range(10):
       cap = cv2.VideoCapture(i)
       if cap.isOpened():
           print(f"摄像头 {i} 可用")
           cap.release()
   ```

2. **权限问题**: 检查应用权限设置
3. **驱动问题**: 更新摄像头驱动

#### 🔍 AI模型问题
1. **TensorFlow错误**: 检查CUDA配置
2. **MediaPipe失败**: 验证模型文件
3. **内存不足**: 调整批处理大小

### ✅ 性能调试
1. **CPU使用率监控**
2. **内存泄漏检测**
3. **响应时间测试**
4. **并发性能验证**

## 📊 代码质量规范

### ✅ 代码结构标准
```
项目根目录/
├── bagua_clock.py              # 主程序入口
├── advanced_health_monitor.py  # 健康监测模块
├── compile_bagua_clock_full_ai.py  # 编译脚本
├── *.spec                      # PyInstaller配置
├── *.json                      # 配置文件
├── compile_logs/               # 编译日志
├── dist/                       # 编译输出
└── docs/                       # 文档文件
```

### ✅ 命名规范
- **文件名**: 小写+下划线 (snake_case)
- **类名**: 大驼峰 (PascalCase)
- **函数名**: 小写+下划线 (snake_case)
- **常量**: 全大写+下划线 (UPPER_CASE)
- **变量**: 小写+下划峰 (camelCase 或 snake_case)

### ✅ 注释规范
```python
def analyze_face_health(self, frame):
    """
    分析面部健康状况
    
    Args:
        frame (numpy.ndarray): 输入的图像帧
        
    Returns:
        dict: 包含健康分析结果的字典
        
    Raises:
        ValueError: 当输入图像无效时
    """
    pass
```

## 🔒 版本管理规范

### ✅ 版本号格式
- **格式**: vX.Y.Z (主版本.次版本.修订版本)
- **当前版本**: v1.1.1 跟踪框修复版
- **版本标记**: 在所有相关文件中保持一致

### ✅ Git工作流程
1. **主分支**: main (稳定版本)
2. **开发分支**: develop (开发版本)
3. **功能分支**: feature/功能名
4. **修复分支**: hotfix/问题描述

### ✅ 提交规范
```
<类型>(<范围>): <描述>

例如:
feat(health): 添加AI舌诊功能
fix(ui): 修复跟踪框显示问题
docs(readme): 更新使用说明
```

## 📝 测试规范

### ✅ 测试类型
1. **单元测试**: 每个函数模块
2. **集成测试**: 模块间交互
3. **系统测试**: 完整功能流程
4. **用户验收测试**: 最终用户场景

### ✅ 测试用例设计
```python
import unittest

class TestHealthMonitor(unittest.TestCase):
    def setUp(self):
        """测试前的准备工作"""
        pass
        
    def test_face_detection(self):
        """测试人脸检测功能"""
        pass
        
    def tearDown(self):
        """测试后的清理工作"""
        pass
```

### ✅ 自动化测试
- **持续集成**: GitHub Actions / Jenkins
- **代码覆盖率**: coverage.py
- **性能测试**: pytest-benchmark

## 🚀 部署规范

### ✅ 发布前检查
1. **功能测试**: 所有功能正常工作
2. **性能测试**: 满足性能要求
3. **兼容性测试**: 多系统环境验证
4. **文档更新**: 使用说明和技术文档

### ✅ 发布流程
1. **创建发布分支**
2. **最终测试验证**
3. **打包编译**
4. **版本标记**
5. **发布说明**

### ✅ 回滚策略
- **备份策略**: 自动备份上一版本
- **快速回滚**: 一键回滚机制
- **问题追踪**: 详细的问题记录

## 🛡️ 安全规范

### ✅ 代码安全
1. **输入验证**: 所有用户输入必须验证
2. **异常处理**: 完善的错误处理机制
3. **资源管理**: 及时释放系统资源
4. **权限控制**: 最小权限原则

### ✅ 数据安全
1. **本地存储**: 敏感数据本地加密
2. **隐私保护**: 不上传用户数据
3. **访问控制**: 用户数据访问控制
4. **审计日志**: 操作日志记录

## 📞 技术支持

### ✅ 问题报告流程
1. **问题描述**: 详细描述问题现象
2. **环境信息**: 系统版本、硬件配置
3. **重现步骤**: 详细的操作步骤
4. **日志文件**: 相关的错误日志
5. **影响评估**: 问题的严重程度

### ✅ 联系方式
- **技术支持**: 湖南全航信息通信有限公司
- **邮箱支持**: [技术支持邮箱]
- **在线文档**: [项目文档地址]
- **问题追踪**: [Issue跟踪系统]

---

## 🎯 最佳实践总结

### ✅ 开发最佳实践
1. **代码复用**: 提取公共组件和工具函数
2. **模块化设计**: 降低模块间耦合度
3. **文档先行**: 重要功能先写文档
4. **测试驱动**: 关键功能必须有测试
5. **持续重构**: 定期优化代码结构

### ✅ 调试最佳实践
1. **日志优先**: 完善的日志记录
2. **渐进调试**: 从简单到复杂逐步调试
3. **工具辅助**: 充分利用调试工具
4. **经验总结**: 记录和分享调试经验

### ✅ 编译最佳实践
1. **环境隔离**: 使用虚拟环境
2. **依赖锁定**: 精确的版本控制
3. **增量编译**: 只编译变更部分
4. **自动化脚本**: 减少手动操作

---

**文档版本**: v1.1.1 跟踪框修复版  
**最后更新**: 2025年7月8日  
**维护者**: 湖南全航信息通信有限公司开发团队  
**适用项目**: 传统健康智慧时钟 v1.1.1+

**注意**: 本文档将随项目发展持续更新，请定期查看最新版本。 