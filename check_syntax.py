#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查advanced_health_monitor.py的语法错误
"""

def check_syntax():
    """检查语法错误"""
    try:
        with open('advanced_health_monitor.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"总行数: {len(lines)}")
        
        # 检查第18480-18490行
        for i in range(18480, 18490):
            if i < len(lines):
                print(f"第{i+1}行: {repr(lines[i])}")
            else:
                print(f"第{i+1}行: 不存在")
        
        # 尝试编译
        import py_compile
        py_compile.compile('advanced_health_monitor.py', doraise=True)
        print("✅ 语法检查通过")
        
    except Exception as e:
        print(f"❌ 语法错误: {e}")

if __name__ == "__main__":
    check_syntax() 