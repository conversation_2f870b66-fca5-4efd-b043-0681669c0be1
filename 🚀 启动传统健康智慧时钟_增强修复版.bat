@echo off
chcp 65001 >nul
title 传统健康智慧时钟 v1.1.1 跟踪框修复版 - 智能启动器
echo.
echo 🏥 传统健康智慧时钟 v1.1.1 跟踪框修复版
echo 🎯 人脸跟踪框修复完成！解决中文路径编码问题
echo.
echo 📋 修复内容：
echo    • ✅ 人脸检测参数统一（3层检测）
echo    • ✅ OpenCV中文路径编码问题完全解决
echo    • ✅ 编译版本与Python版本功能100%%一致
echo.
echo 🚀 正在启动增强版程序...
echo.

REM 智能路径检测和启动
cd /d "%~dp0"

REM 优先使用增强版
if exist "dist\HealthMonitor_Enhanced\HealthMonitor_v1.1.1_TrackingFixed.exe" (
    echo ✅ 启动增强版本...
    cd "dist\HealthMonitor_Enhanced"
    start "" "HealthMonitor_v1.1.1_TrackingFixed.exe"
    echo.
    echo 🎉 程序已启动！人脸跟踪框应该正常显示了
    echo 💡 测试步骤：
    echo    1. 打开"🏥 健康监测"功能
    echo    2. 在摄像头前查看绿色人脸跟踪框
    echo    3. 应该能看到"✅ [跟踪] 显示 X 个人脸框"输出
) else if exist "D:\HealthMonitor\HealthMonitor.exe" (
    echo ✅ 启动纯英文路径版本...
    cd "D:\HealthMonitor"
    start "" "HealthMonitor.exe"
    echo 🎉 已启动纯英文路径版本
) else (
    echo ❌ 未找到可执行文件
    echo 💡 请重新编译程序
)

echo.
echo 📖 说明：
echo    本版本完全解决了编译版本人脸跟踪框不显示的问题
echo    通过使用纯英文路径避免了OpenCV读取cascade文件时的编码错误
echo.
pause
