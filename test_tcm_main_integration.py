#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TCM功能在主程序中的集成状态
"""

import sys
import traceback

def test_tcm_integration():
    """测试TCM集成功能"""
    print("🔬 测试TCM功能在主程序中的集成状态...")
    print("=" * 60)
    
    try:
        # 1. 测试TCM模块导入
        print("1️⃣ 测试TCM模块导入...")
        try:
            from tcm_ai_integration import TCMHealthIntegration, get_tcm_integration
            print("✅ TCM集成模块导入成功")
        except ImportError as e:
            print(f"❌ TCM集成模块导入失败: {e}")
            return False
        except Exception as e:
            print(f"❌ TCM集成模块异常: {e}")
            return False
        
        # 2. 测试主程序中的TCM标志
        print("\n2️⃣ 测试主程序中的TCM可用性标志...")
        try:
            # 模拟主程序的导入逻辑
            TCM_INTEGRATION_AVAILABLE = True
            try:
                from tcm_ai_integration import TCMHealthIntegration, get_tcm_integration
                print("✅ [TCM-AI] TCM知识图谱集成模块加载成功")
            except ImportError as e:
                TCM_INTEGRATION_AVAILABLE = False
                print(f"⚠️ [TCM-AI] TCM集成模块未找到: {e}")
            except Exception as e:
                TCM_INTEGRATION_AVAILABLE = False
                print(f"❌ [TCM-AI] TCM集成初始化失败: {e}")
            
            print(f"TCM_INTEGRATION_AVAILABLE = {TCM_INTEGRATION_AVAILABLE}")
            
        except Exception as e:
            print(f"❌ TCM标志测试失败: {e}")
            return False
        
        # 3. 测试TCM集成器创建
        print("\n3️⃣ 测试TCM集成器创建...")
        try:
            tcm_integration = get_tcm_integration()
            print("✅ TCM集成器创建成功")
            print(f"集成器类型: {type(tcm_integration)}")
        except Exception as e:
            print(f"❌ TCM集成器创建失败: {e}")
            return False
        
        # 4. 测试模拟健康分析增强
        print("\n4️⃣ 测试健康分析增强功能...")
        try:
            # 创建模拟的面部分析数据
            mock_face_analysis = {
                "face_detected": True,
                "face_roi": "mock_roi",
                "real_features": {
                    "brightness_mean": 150,
                    "saturation_mean": 80,
                    "texture_variance": 0.5,
                    "skin_uniformity": 0.7
                },
                "face_color_analysis": {
                    "dominant_color": "yellow",
                    "color_description": "偏黄"
                }
            }
            
            # 执行增强分析
            enhanced_analysis = tcm_integration.enhance_health_analysis(mock_face_analysis)
            print("✅ 健康分析增强成功")
            
            # 检查增强结果
            if "tcm_analysis" in enhanced_analysis:
                tcm_data = enhanced_analysis["tcm_analysis"]
                print(f"✅ TCM分析数据已添加")
                
                # 检查关键字段
                if "tcm_diagnosis" in tcm_data:
                    diagnosis = tcm_data["tcm_diagnosis"]
                    print(f"  • 面诊结果: {diagnosis.get('face_color', '未知')}")
                    print(f"  • 眼部分析: {diagnosis.get('eye_features', {}).get('primary_syndrome', '未知')}")
                    print(f"  • 唇部分析: {diagnosis.get('lip_features', {}).get('primary_syndrome', '未知')}")
                
                if "syndrome_analysis" in tcm_data:
                    syndrome = tcm_data["syndrome_analysis"]
                    print(f"  • 主要证候: {syndrome.get('primary_syndrome', '未知')}")
                    print(f"  • 分析置信度: {syndrome.get('syndrome_confidence', 0):.2f}")
                
                if "herb_recommendations" in tcm_data:
                    herbs = tcm_data["herb_recommendations"]
                    print(f"  • 推荐中药数量: {len(herbs)}种")
                    if herbs:
                        first_herb = herbs[0]
                        print(f"    - 首推中药: {first_herb.get('name', '未知')} ({first_herb.get('nature', '未知')})")
                
            else:
                print("⚠️ 未找到TCM分析数据")
                
        except Exception as e:
            print(f"❌ 健康分析增强失败: {e}")
            traceback.print_exc()
            return False
        
        # 5. 测试集成状态
        print("\n5️⃣ 测试集成状态查询...")
        try:
            status = tcm_integration.get_integration_status()
            print("✅ 集成状态查询成功")
            print(f"  • 集成状态: {'✅ 启用' if status.get('tcm_available') else '❌ 禁用'}")
            print(f"  • 知识图谱: {'✅ 已加载' if status.get('knowledge_graph_loaded') else '❌ 未加载'}")
            print(f"  • 实体类型: {status.get('entity_types', 0)}种")
            print(f"  • 关系类型: {status.get('relation_types', 0)}种")
        except Exception as e:
            print(f"❌ 集成状态查询失败: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 TCM功能集成测试全部通过！")
        print("✅ TCM Knowledge Graph Framework已成功集成到主程序中")
        print("✅ 所有核心功能正常工作")
        return True
        
    except Exception as e:
        print(f"\n❌ TCM集成测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tcm_integration()
    sys.exit(0 if success else 1)
