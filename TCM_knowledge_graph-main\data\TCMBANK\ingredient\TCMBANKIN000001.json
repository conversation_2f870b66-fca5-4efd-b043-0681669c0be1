{"status": true, "code": 0, "message": "fetch success", "data": {"chart_data": [{"TCMBank_ID": "TCMBANKIN000001", "name": "sa<PERSON><PERSON>in a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE003973", "name": "Bamboole<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004172", "name": "root of Chinese Thorowax;<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004524", "name": "all-grass of common knotgrass;Polygonum aviculare", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004641", "name": "Sickle-leaved Hare’s-ear", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004980", "name": "<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE006024", "name": "Smallleaf Black Thorowax", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE006304", "name": "<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE006609", "name": "Siberia Thorowax", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE007288", "name": "<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE007334", "name": "<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000567", "name": "CDKN2A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000824", "name": "RB1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000898", "name": "MYC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001132", "name": "BAX", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001156", "name": "CASP3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001166", "name": "BCL2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE003065", "name": "NXT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000085", "name": "Inflammation", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN010965", "name": "myrtanol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN027725", "name": "<PERSON><PERSON><PERSON><PERSON> c", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000028", "name": "coumarin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000064", "name": "diosgenin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000100", "name": "lauric acid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000102", "name": "pet<PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN008438", "name": "Saikosaponin K", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN019583", "name": "4-o-acetylsaikosaponin d", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN010290", "name": "coprine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN006545", "name": "5,7,3 ', 4'-tetrahydroxy-flavonols-3-O- rutinoside(rutin)", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN007867", "name": "sacranoside a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN060244", "name": "<PERSON><PERSON><PERSON>in b3", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN060247", "name": "Saikosaponin D", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN013747", "name": "cis-6,7-di-hydroxyligustilide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN028081", "name": "α-spinasteryl-3-O-β-D-glucoside", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN019994", "name": "16α,23,28,30-tetrahydroxyolean-11,13(18)-dien-3β-yl-β-d-glucopyranosyl-(1→3)-β-d-fucopyranoside", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN059511", "name": "<PERSON><PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000031", "name": "vitamin k2", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036917", "name": "apigenin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000006", "name": "Nasopharyngeal Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN000023", "name": "acetyl-11-keto-β-boswellicacid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036835", "name": "w<PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE003717", "name": "Hot pepper", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003734", "name": "root of common htreewingnut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000072", "name": "proscillaridin a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036830", "name": "triptonide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000024", "name": "7-dehydrocholesterol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000027", "name": "taxol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE003724", "name": "rhizome of Common Turmeric", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003726", "name": "Cassia Bark", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000003", "name": "rotenone", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000030", "name": "cocaine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN014187", "name": "seo-wenyujinacid B", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000043", "name": "Lymphoma, T-Cell, Cutaneous", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE000001", "name": "Turpentine Oil", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE000002", "name": "Pomegranate fruit", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000004", "name": "quercetagetin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000012", "name": "cannabichromene", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKGE000001", "name": "CXCR2", "record_type": "Targets"}], "data_links": [{"source": 0, "target": 1}, {"source": 0, "target": 2}, {"source": 0, "target": 3}, {"source": 0, "target": 4}, {"source": 0, "target": 5}, {"source": 0, "target": 6}, {"source": 0, "target": 7}, {"source": 0, "target": 8}, {"source": 0, "target": 9}, {"source": 0, "target": 10}, {"source": 0, "target": 11}, {"source": 0, "target": 12}, {"source": 0, "target": 13}, {"source": 0, "target": 14}, {"source": 0, "target": 15}, {"source": 0, "target": 16}, {"source": 0, "target": 17}, {"source": 0, "target": 18}, {"source": 1, "target": 19}, {"source": 1, "target": 20}, {"source": 2, "target": 21}, {"source": 2, "target": 22}, {"source": 3, "target": 23}, {"source": 3, "target": 24}, {"source": 4, "target": 25}, {"source": 4, "target": 26}, {"source": 5, "target": 27}, {"source": 6, "target": 28}, {"source": 6, "target": 29}, {"source": 7, "target": 30}, {"source": 7, "target": 31}, {"source": 8, "target": 32}, {"source": 8, "target": 33}, {"source": 9, "target": 34}, {"source": 10, "target": 35}, {"source": 11, "target": 36}, {"source": 11, "target": 37}, {"source": 11, "target": 38}, {"source": 11, "target": 39}, {"source": 11, "target": 40}, {"source": 12, "target": 41}, {"source": 12, "target": 42}, {"source": 12, "target": 43}, {"source": 12, "target": 44}, {"source": 13, "target": 45}, {"source": 13, "target": 46}, {"source": 13, "target": 47}, {"source": 13, "target": 48}, {"source": 14, "target": 49}, {"source": 14, "target": 50}, {"source": 15, "target": 51}, {"source": 15, "target": 52}, {"source": 15, "target": 53}, {"source": 15, "target": 54}, {"source": 16, "target": 55}, {"source": 17, "target": 56}, {"source": 17, "target": 57}, {"source": 17, "target": 58}, {"source": 18, "target": 59}, {"source": 18, "target": 60}, {"source": 18, "target": 61}, {"source": 18, "target": 62}, {"source": 18, "target": 63}], "depth_chart_all_data": [[{"TCMBank_ID": "TCMBANKIN000001", "name": "sa<PERSON><PERSON>in a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE003973", "name": "Bamboole<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004172", "name": "root of Chinese Thorowax;<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004524", "name": "all-grass of common knotgrass;Polygonum aviculare", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004641", "name": "Sickle-leaved Hare’s-ear", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004980", "name": "<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE006024", "name": "Smallleaf Black Thorowax", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE006304", "name": "<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE006609", "name": "Siberia Thorowax", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE007288", "name": "<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE007334", "name": "<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000567", "name": "CDKN2A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000824", "name": "RB1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000898", "name": "MYC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001132", "name": "BAX", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001156", "name": "CASP3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001166", "name": "BCL2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE003065", "name": "NXT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000085", "name": "Inflammation", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKHE003973", "name": "Bamboole<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN010965", "name": "myrtanol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN027725", "name": "<PERSON><PERSON><PERSON><PERSON> c", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE004172", "name": "root of Chinese Thorowax;<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000028", "name": "coumarin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000064", "name": "diosgenin", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE004524", "name": "all-grass of common knotgrass;Polygonum aviculare", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000100", "name": "lauric acid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000102", "name": "pet<PERSON><PERSON>", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE004641", "name": "Sickle-leaved Hare’s-ear", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN008438", "name": "Saikosaponin K", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN019583", "name": "4-o-acetylsaikosaponin d", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE004980", "name": "<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN008438", "name": "Saikosaponin K", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN010290", "name": "coprine", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE006024", "name": "Smallleaf Black Thorowax", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN006545", "name": "5,7,3 ', 4'-tetrahydroxy-flavonols-3-O- rutinoside(rutin)", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN007867", "name": "sacranoside a", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE006304", "name": "<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN060244", "name": "<PERSON><PERSON><PERSON>in b3", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN060247", "name": "Saikosaponin D", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE006609", "name": "Siberia Thorowax", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN013747", "name": "cis-6,7-di-hydroxyligustilide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN028081", "name": "α-spinasteryl-3-O-β-D-glucoside", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE007288", "name": "<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN008438", "name": "Saikosaponin K", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN019994", "name": "16α,23,28,30-tetrahydroxyolean-11,13(18)-dien-3β-yl-β-d-glucopyranosyl-(1→3)-β-d-fucopyranoside", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKHE007334", "name": "<PERSON><PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN010965", "name": "myrtanol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN059511", "name": "<PERSON><PERSON><PERSON>", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKGE000567", "name": "CDKN2A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000031", "name": "vitamin k2", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036917", "name": "apigenin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000006", "name": "Nasopharyngeal Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000824", "name": "RB1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000023", "name": "acetyl-11-keto-β-boswellicacid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036835", "name": "w<PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000898", "name": "MYC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003717", "name": "Hot pepper", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003734", "name": "root of common htreewingnut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000072", "name": "proscillaridin a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036830", "name": "triptonide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE001132", "name": "BAX", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000024", "name": "7-dehydrocholesterol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000027", "name": "taxol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE001156", "name": "CASP3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003724", "name": "rhizome of Common Turmeric", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003726", "name": "Cassia Bark", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000003", "name": "rotenone", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000030", "name": "cocaine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE001166", "name": "BCL2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003726", "name": "Cassia Bark", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000024", "name": "7-dehydrocholesterol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000027", "name": "taxol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE003065", "name": "NXT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN014187", "name": "seo-wenyujinacid B", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000043", "name": "Lymphoma, T-Cell, Cutaneous", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKDI000085", "name": "Inflammation", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE000001", "name": "Turpentine Oil", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE000002", "name": "Pomegranate fruit", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000004", "name": "quercetagetin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000012", "name": "cannabichromene", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKGE000001", "name": "CXCR2", "record_type": "Targets"}]]}, "url": ""}