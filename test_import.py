import sys
import os

print("开始测试导入...")

try:
    # 添加当前目录到路径
    sys.path.insert(0, os.getcwd())
    print("当前目录已添加到Python路径")
    
    # 测试导入skindetector
    print("尝试导入skindetector...")
    from skindetector import SkinDetector
    print("✅ skindetector导入成功")
    
    # 测试导入集成模块
    print("尝试导入skin_detector_integration...")
    from skin_detector_integration import SkinDetectorAdapter
    print("✅ skin_detector_integration导入成功")
    
    # 测试实例化
    print("尝试实例化SkinDetector...")
    detector = SkinDetector()
    print("✅ SkinDetector实例化成功")
    
    print("🎉 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc() 