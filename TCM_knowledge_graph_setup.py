#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCM Knowledge Graph Framework Setup and Usage Guide
传统中医知识图谱框架安装和使用指南

This script helps you set up and use the TCM Knowledge Graph framework.
该脚本帮助您设置和使用传统中医知识图谱框架。
"""

import os
import sys
import subprocess
import json
from pathlib import Path

class TCMKnowledgeGraphSetup:
    def __init__(self):
        self.project_dir = Path(__file__).parent / "TCM_knowledge_graph-main"
        self.data_dir = self.project_dir / "data"
        self.processed_code_dir = self.project_dir / "processed_code"
        self.merge_result_dir = self.project_dir / "merge_result"
        
    def check_dependencies(self):
        """检查必要的Python依赖包"""
        required_packages = [
            'pandas',
            'numpy',
            'tqdm',
            'json',
            'glob',
            're',
            'sentence-transformers',  # 用于症状相似度计算
            'openai',  # 如果使用ChatGPT API
            'transformers',  # 用于ChatGLM
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✓ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                print(f"✗ {package} 未安装")
        
        if missing_packages:
            print(f"\n需要安装以下包: {', '.join(missing_packages)}")
            print("运行以下命令安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        return True
    
    def setup_project_structure(self):
        """设置项目目录结构"""
        print("设置项目目录结构...")
        
        # 创建必要的目录
        directories = [
            self.merge_result_dir,
            self.merge_result_dir / "entity",
            self.merge_result_dir / "relation",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✓ 创建目录: {directory}")
    
    def check_data_availability(self):
        """检查数据文件的可用性"""
        print("\n检查数据文件...")
        
        # 检查各个数据库目录
        databases = {
            "CPMCP": ["cmp", "cpm", "disease", "herb", "ingredient", "target"],
            "TCMBANK": ["disease", "herb", "ingredient"],
            "symmap": ["herb", "mm_symptom", "symptom", "syndrome", "target"],
        }
        
        available_data = {}
        for db_name, subdirs in databases.items():
            db_path = self.data_dir / db_name
            if db_path.exists():
                available_subdirs = []
                for subdir in subdirs:
                    subdir_path = db_path / subdir
                    if subdir_path.exists() and any(subdir_path.iterdir()):
                        available_subdirs.append(subdir)
                        print(f"✓ {db_name}/{subdir} 数据可用")
                    else:
                        print(f"✗ {db_name}/{subdir} 数据缺失")
                available_data[db_name] = available_subdirs
            else:
                print(f"✗ {db_name} 数据库目录不存在")
                available_data[db_name] = []
        
        return available_data
    
    def create_config_file(self):
        """创建配置文件"""
        config = {
            "project_dir": str(self.project_dir),
            "data_dir": str(self.data_dir),
            "merge_result_dir": str(self.merge_result_dir),
            "processing_steps": [
                "extract_herb",
                "extract_prescription", 
                "extract_mm_symptom",
                "extract_syndrome",
                "extract_other_entities",
                "extract_disease",
                "extract_ingredient",
                "extract_target",
                "extract_tcm_symptom"
            ],
            "databases": {
                "CPMCP": "http://cpmcp.top",
                "TCMBANK": "https://tcmbank.cn/",
                "SymMap": "http://www.symmap.org/download/",
                "TCMID": "http://www.megabionet.org/tcmid",
                "PharMeBINet": "https://zenodo.org/records/7009457",
                "PrimeKG": "https://dataverse.harvard.edu/dataset.xhtml?persistentId=doi:10.7910/DVN/IXA7BM"
            }
        }
        
        config_file = self.project_dir / "tcm_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 配置文件已创建: {config_file}")
        return config
    
    def run_processing_step(self, step_name):
        """运行特定的处理步骤"""
        script_path = self.processed_code_dir / f"{step_name}.py"
        
        if not script_path.exists():
            print(f"✗ 脚本不存在: {script_path}")
            return False
        
        print(f"运行处理步骤: {step_name}")
        try:
            # 设置环境变量
            env = os.environ.copy()
            env['PROJECT_DIR'] = str(self.project_dir)
            
            # 运行脚本
            result = subprocess.run(
                [sys.executable, str(script_path)],
                cwd=str(self.processed_code_dir),
                env=env,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✓ {step_name} 处理完成")
                return True
            else:
                print(f"✗ {step_name} 处理失败:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"✗ 运行 {step_name} 时出错: {e}")
            return False
    
    def show_usage_guide(self):
        """显示使用指南"""
        print("\n" + "="*60)
        print("TCM Knowledge Graph Framework 使用指南")
        print("="*60)
        
        print("\n1. 数据准备:")
        print("   - 从各个数据库官网下载数据文件")
        print("   - 将数据文件放置到对应的data目录中")
        
        print("\n2. 处理步骤:")
        processing_steps = [
            ("extract_herb.py", "提取和对齐中药材实体"),
            ("extract_prescription.py", "提取处方信息"),
            ("extract_mm_symptom.py", "提取现代医学症状"),
            ("extract_syndrome.py", "提取中医证候"),
            ("extract_other_entities.py", "提取其他实体"),
            ("extract_disease.py", "提取疾病信息"),
            ("extract_ingredient.py", "提取成分信息"),
            ("extract_target.py", "提取靶点信息"),
            ("merge_tcm_symptom.py", "合并中医症状")
        ]
        
        for script, description in processing_steps:
            print(f"   - {script}: {description}")
        
        print("\n3. 输出结果:")
        print("   - 实体文件: merge_result/entity/")
        print("   - 关系文件: merge_result/relation/")
        
        print("\n4. 知识图谱特点:")
        print("   - 20种实体类型")
        print("   - 46种关系类型") 
        print("   - 3,447,023条记录")
        print("   - 整合6个高质量数据库")

def main():
    """主函数"""
    print("TCM Knowledge Graph Framework Setup")
    print("传统中医知识图谱框架安装程序")
    print("="*50)
    
    setup = TCMKnowledgeGraphSetup()
    
    # 检查依赖
    print("1. 检查Python依赖包...")
    if not setup.check_dependencies():
        print("请先安装缺失的依赖包")
        return
    
    # 设置项目结构
    print("\n2. 设置项目结构...")
    setup.setup_project_structure()
    
    # 检查数据可用性
    print("\n3. 检查数据文件...")
    available_data = setup.check_data_availability()
    
    # 创建配置文件
    print("\n4. 创建配置文件...")
    config = setup.create_config_file()
    
    # 显示使用指南
    setup.show_usage_guide()
    
    print(f"\n✓ TCM Knowledge Graph Framework 设置完成!")
    print(f"项目目录: {setup.project_dir}")
    print(f"配置文件: {setup.project_dir}/tcm_config.json")
    
    # 询问是否开始处理
    if available_data:
        response = input("\n是否开始数据处理? (y/n): ")
        if response.lower() == 'y':
            print("开始数据处理...")
            for step in config["processing_steps"]:
                if setup.run_processing_step(step):
                    print(f"✓ {step} 完成")
                else:
                    print(f"✗ {step} 失败，请检查数据和依赖")
                    break

if __name__ == "__main__":
    main()
