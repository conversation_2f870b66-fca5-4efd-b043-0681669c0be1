#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SkinDetector安装验证脚本
"""

def verify_skindetector():
    """验证SkinDetector安装"""
    print("🔍 验证SkinDetector安装状态...")
    
    try:
        from skindetector import SkinDetector
        print("✅ SkinDetector库导入成功")
        
        # 测试初始化
        detector = SkinDetector()
        print("✅ SkinDetector初始化成功")
        
        # 测试基本功能
        import numpy as np
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:, :] = [255, 200, 150]  # 模拟皮肤颜色
        
        mask = detector.detect(test_image)
        print("✅ SkinDetector检测功能正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ SkinDetector导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ SkinDetector测试失败: {e}")
        return False

def verify_integration():
    """验证集成状态"""
    print("🔍 验证集成状态...")
    
    try:
        # 检查集成模块
        from skin_detector_integration import EnhancedSkinDetector, SkinDetectorAdapter
        print("✅ 集成模块导入成功")
        
        # 检查健康监测系统
        with open('advanced_health_monitor.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'SkinDetector' in content:
                print("✅ 健康监测系统包含SkinDetector集成代码")
                return True
            else:
                print("❌ 健康监测系统未包含SkinDetector集成代码")
                return False
                
    except Exception as e:
        print(f"❌ 集成验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 SkinDetector安装验证")
    print("=" * 40)
    
    # 验证基础环境
    print("\n📋 验证基础环境...")
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except:
        print("❌ OpenCV未安装")
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except:
        print("❌ NumPy未安装")
    
    # 验证SkinDetector
    print("\n📋 验证SkinDetector...")
    skindetector_ok = verify_skindetector()
    
    # 验证集成
    print("\n📋 验证集成...")
    integration_ok = verify_integration()
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 验证结果:")
    print(f"SkinDetector库: {'✅ 正常' if skindetector_ok else '❌ 异常'}")
    print(f"系统集成: {'✅ 正常' if integration_ok else '❌ 异常'}")
    
    if skindetector_ok and integration_ok:
        print("\n🎉 SkinDetector安装和集成完全成功！")
        print("现在可以启动增强版健康监测系统了。")
    elif skindetector_ok:
        print("\n⚠️ SkinDetector库正常，但集成可能有问题")
    else:
        print("\n❌ 需要检查安装和配置")

if __name__ == "__main__":
    main() 