#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCM Knowledge Graph Framework Simple Setup
传统中医知识图谱框架简单安装程序
"""

import os
import sys
from pathlib import Path

def main():
    print("TCM Knowledge Graph Framework 简单安装")
    print("="*50)
    
    # 检查项目目录
    project_dir = Path("TCM_knowledge_graph-main")
    if not project_dir.exists():
        print("✗ TCM_knowledge_graph-main 目录不存在")
        print("请确保已经下载并解压了TCM知识图谱项目")
        return
    
    print(f"✓ 找到项目目录: {project_dir}")
    
    # 检查主要目录
    data_dir = project_dir / "data"
    processed_code_dir = project_dir / "processed_code"
    
    if data_dir.exists():
        print(f"✓ 数据目录存在: {data_dir}")
    else:
        print(f"✗ 数据目录不存在: {data_dir}")
    
    if processed_code_dir.exists():
        print(f"✓ 处理代码目录存在: {processed_code_dir}")
        
        # 列出可用的处理脚本
        scripts = list(processed_code_dir.glob("*.py"))
        print(f"✓ 找到 {len(scripts)} 个处理脚本:")
        for script in scripts:
            print(f"   - {script.name}")
    else:
        print(f"✗ 处理代码目录不存在: {processed_code_dir}")
    
    # 创建输出目录
    merge_result_dir = project_dir / "merge_result"
    merge_result_dir.mkdir(exist_ok=True)
    
    entity_dir = merge_result_dir / "entity"
    entity_dir.mkdir(exist_ok=True)
    
    relation_dir = merge_result_dir / "relation"
    relation_dir.mkdir(exist_ok=True)
    
    print(f"✓ 创建输出目录: {merge_result_dir}")
    print(f"✓ 创建实体目录: {entity_dir}")
    print(f"✓ 创建关系目录: {relation_dir}")
    
    # 检查数据库目录
    print("\n检查数据库目录:")
    databases = ["CPMCP", "TCMBANK", "symmap", "ChatGLM-6B", "gpt_data", "resource"]
    
    for db in databases:
        db_path = data_dir / db
        if db_path.exists():
            files_count = len(list(db_path.rglob("*")))
            print(f"✓ {db}: {files_count} 个文件/目录")
        else:
            print(f"✗ {db}: 目录不存在")
    
    # 检查必要的Python包
    print("\n检查Python依赖包:")
    required_packages = ['pandas', 'numpy', 'tqdm']
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            print(f"   请运行: pip install {package}")
    
    print("\n" + "="*50)
    print("TCM Knowledge Graph Framework 安装检查完成!")
    print("\n使用说明:")
    print("1. 确保所有依赖包已安装")
    print("2. 从各数据库官网下载数据文件到data目录")
    print("3. 运行processed_code目录中的处理脚本")
    print("4. 处理结果将保存在merge_result目录中")
    
    print(f"\n项目目录: {project_dir.absolute()}")
    print(f"数据目录: {data_dir.absolute()}")
    print(f"输出目录: {merge_result_dir.absolute()}")

if __name__ == "__main__":
    main()
