#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SkinDetector调试脚本
"""

import cv2
import numpy as np

def test_skindetector_direct():
    """直接测试SkinDetector"""
    print("🧪 直接测试SkinDetector...")
    
    try:
        from skindetector import SkinDetector
        detector = SkinDetector()
        print("✅ SkinDetector创建成功")
        
        # 创建测试图像
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        test_image[:, :] = [255, 200, 150]  # 模拟皮肤颜色
        
        # 测试检测
        mask = detector.detect(test_image)
        print(f"✅ 检测成功，掩码形状: {mask.shape}")
        
        # 测试健康分析
        health_analysis = detector.analyze_skin_health(test_image)
        print(f"✅ 健康分析成功: {health_analysis}")
        
        return True
        
    except Exception as e:
        print(f"❌ SkinDetector测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_module():
    """测试集成模块"""
    print("🧪 测试集成模块...")
    
    try:
        from skin_detector_integration import EnhancedSkinDetector, SkinDetectorAdapter
        print("✅ 集成模块导入成功")
        
        # 测试增强检测器
        detector = EnhancedSkinDetector()
        print("✅ 增强检测器创建成功")
        
        # 创建测试图像
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        test_image[:, :] = [255, 200, 150]
        
        # 测试检测
        result = detector.detect_skin_regions(test_image)
        print(f"✅ 检测结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_health_monitor_integration():
    """测试健康监测系统集成"""
    print("🧪 测试健康监测系统集成...")
    
    try:
        # 模拟健康监测系统的初始化
        from skin_detector_integration import SkinDetectorAdapter
        
        # 创建一个模拟的健康监测实例
        class MockHealthMonitor:
            def __init__(self):
                self.name = "MockHealthMonitor"
        
        mock_monitor = MockHealthMonitor()
        adapter = SkinDetectorAdapter(mock_monitor)
        print("✅ 适配器创建成功")
        
        # 测试增强分析
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        test_image[:, :] = [255, 200, 150]
        
        existing_analysis = {
            "skin_health_score": 75.0,
            "skin_uniformity": 0.8,
            "texture_quality": 85.0
        }
        
        enhanced_result = adapter.enhance_skin_analysis(test_image, existing_analysis)
        print(f"✅ 增强分析结果: {enhanced_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 健康监测集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_actual_runtime():
    """检查实际运行时状态"""
    print("🧪 检查实际运行时状态...")
    
    try:
        # 检查SkinDetector是否可用
        try:
            from skindetector import SkinDetector
            print("✅ SkinDetector库可用")
            skindetector_available = True
        except ImportError:
            print("❌ SkinDetector库不可用")
            skindetector_available = False
        
        # 检查集成模块是否可用
        try:
            from skin_detector_integration import EnhancedSkinDetector, SkinDetectorAdapter
            print("✅ 集成模块可用")
            integration_available = True
        except ImportError:
            print("❌ 集成模块不可用")
            integration_available = False
        
        # 检查健康监测系统中的集成代码
        with open('advanced_health_monitor.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'SKINDETECTOR_INTEGRATION_AVAILABLE' in content:
                print("✅ 健康监测系统包含集成检查代码")
            else:
                print("❌ 健康监测系统缺少集成检查代码")
            
            if 'skin_detector_adapter' in content:
                print("✅ 健康监测系统包含适配器代码")
            else:
                print("❌ 健康监测系统缺少适配器代码")
            
            if '_enhance_with_skindetector' in content:
                print("✅ 健康监测系统包含增强函数")
            else:
                print("❌ 健康监测系统缺少增强函数")
        
        return skindetector_available and integration_available
        
    except Exception as e:
        print(f"❌ 运行时状态检查失败: {e}")
        return False

def main():
    """主调试函数"""
    print("🚀 SkinDetector调试开始")
    print("=" * 50)
    
    tests = [
        ("SkinDetector库", test_skindetector_direct),
        ("集成模块", test_integration_module),
        ("健康监测集成", test_health_monitor_integration),
        ("运行时状态", check_actual_runtime)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        results[test_name] = test_func()
    
    print("\n" + "=" * 50)
    print("📊 调试结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    # 分析问题
    if not results["SkinDetector库"]:
        print("\n🔍 问题分析: SkinDetector库未正确安装或导入")
    elif not results["集成模块"]:
        print("\n🔍 问题分析: 集成模块有问题")
    elif not results["健康监测集成"]:
        print("\n🔍 问题分析: 健康监测系统集成有问题")
    elif not results["运行时状态"]:
        print("\n🔍 问题分析: 运行时状态检查失败")
    else:
        print("\n🎉 所有测试通过！SkinDetector应该正常工作")

if __name__ == "__main__":
    main() 