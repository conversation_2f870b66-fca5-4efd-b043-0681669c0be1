#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终SkinDetector集成测试
"""

import sys
import os
import numpy as np

def test_skindetector_integration():
    """测试SkinDetector集成"""
    print("🧪 最终SkinDetector集成测试")
    print("=" * 50)
    
    try:
        # 添加当前目录到路径
        sys.path.insert(0, os.getcwd())
        
        # 1. 测试模块导入
        print("📋 步骤1: 测试模块导入")
        from skindetector import SkinDetector
        print("✅ skindetector模块导入成功")
        
        from skin_detector_integration import SkinDetectorAdapter
        print("✅ skin_detector_integration模块导入成功")
        
        # 2. 测试主程序导入
        print("\n📋 步骤2: 测试主程序导入")
        import advanced_health_monitor
        print("✅ 主程序导入成功")
        print(f"✅ SKINDETECTOR_INTEGRATION_AVAILABLE: {advanced_health_monitor.SKINDETECTOR_INTEGRATION_AVAILABLE}")
        
        # 3. 测试SkinDetector实例化
        print("\n📋 步骤3: 测试SkinDetector实例化")
        detector = SkinDetector()
        print("✅ SkinDetector实例化成功")
        
        # 4. 创建测试图像
        print("\n📋 步骤4: 创建测试图像")
        test_image = np.zeros((300, 300, 3), dtype=np.uint8)
        test_image[50:250, 50:250] = [255, 200, 150]  # 模拟皮肤颜色
        print("✅ 测试图像创建成功")
        
        # 5. 测试SkinDetector检测
        print("\n📋 步骤5: 测试SkinDetector检测")
        health_analysis = detector.analyze_skin_health(test_image)
        confidence = detector.get_detection_confidence(test_image)
        
        print(f"✅ SkinDetector检测结果:")
        print(f"  - 皮肤覆盖率: {health_analysis.get('skin_coverage', 0):.3f}")
        print(f"  - 平均亮度: {health_analysis.get('brightness', 0):.1f}")
        print(f"  - 饱和度: {health_analysis.get('saturation', 0):.1f}")
        print(f"  - 均匀性: {health_analysis.get('uniformity', 0):.3f}")
        print(f"  - 检测置信度: {confidence:.3f}")
        
        # 6. 测试适配器
        print("\n📋 步骤6: 测试适配器")
        class MockHealthMonitor:
            def __init__(self):
                self.name = "MockHealthMonitor"
        
        mock_monitor = MockHealthMonitor()
        adapter = SkinDetectorAdapter(mock_monitor)
        
        # 模拟原有分析结果
        existing_analysis = {
            "skin_health_score": 75.0,
            "skin_uniformity": 0.8,
            "texture_quality": 85.0,
            "brightness_score": 0.7
        }
        
        # 执行增强分析
        enhanced_analysis = adapter.enhance_skin_analysis(test_image, existing_analysis)
        
        print(f"✅ 适配器增强结果:")
        print(f"  - 是否增强: {enhanced_analysis.get('skindetector_enhanced', False)}")
        print(f"  - 原评分: {existing_analysis.get('skin_health_score', 0):.1f}")
        print(f"  - 新评分: {enhanced_analysis.get('skin_health_score', 0):.1f}")
        
        # 7. 检查增强指标
        print("\n📋 步骤7: 检查增强指标")
        required_indicators = [
            "skin_coverage",
            "skin_brightness", 
            "skin_saturation",
            "detection_statistics",
            "analysis_enhanced_by_skindetector"
        ]
        
        missing_indicators = []
        for indicator in required_indicators:
            if indicator in enhanced_analysis:
                print(f"✅ {indicator}: {enhanced_analysis[indicator]}")
            else:
                print(f"❌ {indicator}: 缺失")
                missing_indicators.append(indicator)
        
        # 8. 检查SkinDetector分析结果
        if "skindetector_analysis" in enhanced_analysis:
            skindetector_data = enhanced_analysis["skindetector_analysis"]
            print(f"\n✅ SkinDetector分析结果:")
            print(f"  - 检测方法: {skindetector_data.get('detection_method', 'unknown')}")
            print(f"  - 区域数量: {skindetector_data.get('regions_count', 0)}")
            print(f"  - 置信度: {skindetector_data.get('confidence', 0):.3f}")
            print(f"  - 增强应用: {skindetector_data.get('enhancement_applied', False)}")
        else:
            print("❌ skindetector_analysis: 缺失")
            missing_indicators.append("skindetector_analysis")
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 测试总结:")
        
        if len(missing_indicators) == 0:
            print("🎉 所有增强指标都已正确生成！")
            print("✅ SkinDetector已成功参与皮肤健康计算")
            print("✅ 现在启动健康监测系统应该能看到所有增强指标")
            return True
        else:
            print(f"⚠️ 缺少 {len(missing_indicators)} 个增强指标: {missing_indicators}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 最终SkinDetector集成测试")
    print("=" * 50)
    
    success = test_skindetector_integration()
    
    if success:
        print("\n🎉 测试成功！SkinDetector增强指标已正确生成")
        print("现在启动健康监测系统应该能看到所有增强指标")
        print("\n在【高级皮肤分析】中应该能看到:")
        print("  • 🔥 SkinDetector增强分析:")
        print("    📊 皮肤覆盖率: XX.X%")
        print("    💡 皮肤亮度: XXX.X/255")
        print("    🎨 皮肤饱和度: XXX.X/255")
        print("    🔍 检测统计: X个区域 (置信度: XX.X%)")
        print("    🔥 SkinDetector分析: X个区域 (置信度: XX.X%)")
        print("    ✅ 增强应用: 已启用")
    else:
        print("\n❌ 测试失败，需要修复集成问题")

if __name__ == "__main__":
    main() 