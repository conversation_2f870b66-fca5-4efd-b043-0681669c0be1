import sys
import os

print("开始验证SkinDetector集成...")

try:
    # 添加当前目录到路径
    sys.path.insert(0, os.getcwd())
    
    # 测试导入
    from skindetector import SkinDetector
    print("✅ skindetector导入成功")
    
    from skin_detector_integration import SkinDetectorAdapter
    print("✅ skin_detector_integration导入成功")
    
    # 测试实例化
    detector = SkinDetector()
    print("✅ SkinDetector实例化成功")
    
    # 测试主程序集成标志
    import advanced_health_monitor
    print(f"✅ SKINDETECTOR_INTEGRATION_AVAILABLE: {advanced_health_monitor.SKINDETECTOR_INTEGRATION_AVAILABLE}")
    
    print("🎉 所有验证通过！SkinDetector集成正常")
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc() 