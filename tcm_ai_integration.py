#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCM Knowledge Graph AI Integration Module
传统中医知识图谱AI集成模块

将TCM Knowledge Graph Framework集成到健康监测系统中
实现面部五官分析和中医面诊功能
"""

import pandas as pd
import numpy as np
import cv2
import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import traceback

# 尝试导入TCM知识图谱相关模块
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
    print("✅ [TCM-AI] sentence-transformers 可用")
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("⚠️ [TCM-AI] sentence-transformers 不可用")

class TCMKnowledgeGraphAnalyzer:
    """TCM知识图谱分析器"""
    
    def __init__(self, project_dir=None):
        if project_dir is None:
            project_dir = Path(__file__).parent / "TCM_knowledge_graph-main"
        
        self.project_dir = Path(project_dir)
        self.merge_result_dir = self.project_dir / "merge_result"
        self.entity_dir = self.merge_result_dir / "entity"
        self.relation_dir = self.merge_result_dir / "relation"
        
        # 初始化知识图谱数据
        self.entities = {}
        self.relations = {}
        self.tcm_knowledge_loaded = False
        
        # 初始化句子嵌入模型（用于症状相似度计算）
        self.sentence_model = None
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                # 使用轻量级的中文模型
                self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
                print("✅ [TCM-AI] 句子嵌入模型加载成功")
            except Exception as e:
                print(f"⚠️ [TCM-AI] 句子嵌入模型加载失败: {e}")
        
        # 加载知识图谱数据
        self.load_tcm_knowledge()
        
        # 中医面诊知识库
        self.facial_diagnosis_knowledge = self._initialize_facial_diagnosis_knowledge()
        
        # 五官分析映射
        self.facial_features_mapping = self._initialize_facial_features_mapping()
    
    def load_tcm_knowledge(self):
        """加载TCM知识图谱数据"""
        try:
            print("🔄 [TCM-AI] 加载TCM知识图谱数据...")
            
            # 检查数据目录是否存在
            if not self.entity_dir.exists() or not self.relation_dir.exists():
                print("⚠️ [TCM-AI] 知识图谱数据目录不存在，使用内置知识库")
                self._load_builtin_knowledge()
                return
            
            # 加载实体数据
            entity_files = [
                'medicinal_material.csv',
                'disease.csv', 
                'mm_symptom.csv',
                'syndrome.csv',
                'prescription.csv'
            ]
            
            for file_name in entity_files:
                file_path = self.entity_dir / file_name
                if file_path.exists():
                    try:
                        df = pd.read_csv(file_path, encoding='utf-8')
                        entity_type = file_name.replace('.csv', '')
                        self.entities[entity_type] = df
                        print(f"✅ [TCM-AI] 加载实体: {entity_type} ({len(df)} 条记录)")
                    except Exception as e:
                        print(f"⚠️ [TCM-AI] 加载实体失败 {file_name}: {e}")
            
            # 加载关系数据
            relation_files = [
                'herb2syndrome.csv',
                'disease2mm_symptom.csv',
                'prescription2medicinal_material.csv'
            ]
            
            for file_name in relation_files:
                file_path = self.relation_dir / file_name
                if file_path.exists():
                    try:
                        df = pd.read_csv(file_path, encoding='utf-8')
                        relation_type = file_name.replace('.csv', '')
                        self.relations[relation_type] = df
                        print(f"✅ [TCM-AI] 加载关系: {relation_type} ({len(df)} 条记录)")
                    except Exception as e:
                        print(f"⚠️ [TCM-AI] 加载关系失败 {file_name}: {e}")
            
            self.tcm_knowledge_loaded = len(self.entities) > 0 or len(self.relations) > 0
            
            if not self.tcm_knowledge_loaded:
                print("⚠️ [TCM-AI] 未找到有效的知识图谱数据，使用内置知识库")
                self._load_builtin_knowledge()
            else:
                print(f"✅ [TCM-AI] 知识图谱加载完成: {len(self.entities)}个实体类型, {len(self.relations)}个关系类型")
                
        except Exception as e:
            print(f"❌ [TCM-AI] 知识图谱加载失败: {e}")
            self._load_builtin_knowledge()
    
    def _load_builtin_knowledge(self):
        """加载内置的中医知识库"""
        print("🔄 [TCM-AI] 加载内置中医知识库...")
        
        # 内置中药材数据
        self.entities['medicinal_material'] = pd.DataFrame([
            {'chinese_name': '人参', 'english_name': 'Ginseng', 'property': '温', 'flavour': '甘', 'tropism': '心脾肺'},
            {'chinese_name': '黄芪', 'english_name': 'Astragalus', 'property': '微温', 'flavour': '甘', 'tropism': '脾肺'},
            {'chinese_name': '当归', 'english_name': 'Angelica', 'property': '温', 'flavour': '甘辛', 'tropism': '心肝脾'},
            {'chinese_name': '白术', 'english_name': 'Atractylodes', 'property': '温', 'flavour': '甘苦', 'tropism': '脾胃'},
            {'chinese_name': '茯苓', 'english_name': 'Poria', 'property': '平', 'flavour': '甘淡', 'tropism': '心脾肾'}
        ])
        
        # 内置症状数据
        self.entities['mm_symptom'] = pd.DataFrame([
            {'symptom_name': '面色苍白', 'category': '面部', 'severity': '中等'},
            {'symptom_name': '面色潮红', 'category': '面部', 'severity': '轻微'},
            {'symptom_name': '眼袋浮肿', 'category': '眼部', 'severity': '轻微'},
            {'symptom_name': '黑眼圈', 'category': '眼部', 'severity': '轻微'},
            {'symptom_name': '唇色暗淡', 'category': '唇部', 'severity': '中等'}
        ])
        
        # 内置证候数据
        self.entities['syndrome'] = pd.DataFrame([
            {'syndrome_name': '气虚证', 'description': '气虚乏力，面色苍白', 'category': '虚证'},
            {'syndrome_name': '血虚证', 'description': '血虚面白，唇甲色淡', 'category': '虚证'},
            {'syndrome_name': '阴虚证', 'description': '阴虚内热，面色潮红', 'category': '虚证'},
            {'syndrome_name': '阳虚证', 'description': '阳虚畏寒，面色㿠白', 'category': '虚证'},
            {'syndrome_name': '湿热证', 'description': '湿热内蕴，面色黄腻', 'category': '实证'}
        ])
        
        self.tcm_knowledge_loaded = True
        print("✅ [TCM-AI] 内置知识库加载完成")
    
    def _initialize_facial_diagnosis_knowledge(self):
        """初始化中医面诊知识库"""
        return {
            "face_color": {
                "pale": {"syndrome": "气虚证", "description": "面色苍白，气血不足", "herbs": ["人参", "黄芪", "白术"]},
                "red": {"syndrome": "阴虚证", "description": "面色潮红，阴虚内热", "herbs": ["生地", "麦冬", "玄参"]},
                "yellow": {"syndrome": "脾虚证", "description": "面色萎黄，脾胃虚弱", "herbs": ["党参", "白术", "茯苓"]},
                "dark": {"syndrome": "肾虚证", "description": "面色黧黑，肾气不足", "herbs": ["熟地", "山药", "枸杞"]}
            },
            "eye_features": {
                "puffy": {"syndrome": "脾虚湿盛", "description": "眼袋浮肿，脾虚水湿", "herbs": ["茯苓", "白术", "陈皮"]},
                "dark_circles": {"syndrome": "肾虚证", "description": "黑眼圈，肾精不足", "herbs": ["熟地", "枸杞", "菟丝子"]},
                "dry": {"syndrome": "肝血虚", "description": "眼干涩，肝血不足", "herbs": ["当归", "白芍", "枸杞"]}
            },
            "lip_features": {
                "pale": {"syndrome": "血虚证", "description": "唇色淡白，血虚不荣", "herbs": ["当归", "熟地", "白芍"]},
                "purple": {"syndrome": "血瘀证", "description": "唇色紫暗，血行不畅", "herbs": ["红花", "桃仁", "川芎"]},
                "dry": {"syndrome": "津液不足", "description": "唇干燥，津液亏虚", "herbs": ["麦冬", "玄参", "石斛"]}
            }
        }
    
    def _initialize_facial_features_mapping(self):
        """初始化五官分析映射"""
        return {
            "skin_analysis": {
                "brightness_low": "面色暗淡",
                "brightness_high": "面色光泽",
                "uniformity_low": "面色不均",
                "saturation_low": "面色苍白",
                "saturation_high": "面色红润"
            },
            "facial_regions": {
                "forehead": "额部",
                "cheeks": "面颊",
                "nose": "鼻部", 
                "mouth": "口唇",
                "chin": "下颌"
            }
        }
    
    def analyze_facial_features_tcm(self, face_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """基于TCM知识图谱分析面部特征"""
        try:
            print("🔄 [TCM-AI] 开始中医面诊分析...")
            
            tcm_analysis = {
                "timestamp": datetime.now().isoformat(),
                "tcm_diagnosis": {},
                "syndrome_analysis": {},
                "herb_recommendations": [],
                "constitution_type": "平和质",
                "health_suggestions": [],
                "confidence_score": 0.0
            }
            
            # 分析面色
            face_color_analysis = self._analyze_face_color(face_analysis)
            tcm_analysis["tcm_diagnosis"]["face_color"] = face_color_analysis
            
            # 分析眼部特征
            eye_analysis = self._analyze_eye_features(face_analysis)
            tcm_analysis["tcm_diagnosis"]["eye_features"] = eye_analysis
            
            # 分析唇部特征
            lip_analysis = self._analyze_lip_features(face_analysis)
            tcm_analysis["tcm_diagnosis"]["lip_features"] = lip_analysis
            
            # 综合证候分析
            syndrome_result = self._comprehensive_syndrome_analysis(
                face_color_analysis, eye_analysis, lip_analysis
            )
            tcm_analysis["syndrome_analysis"] = syndrome_result
            
            # 生成中药推荐
            herb_recommendations = self._generate_herb_recommendations(syndrome_result)
            tcm_analysis["herb_recommendations"] = herb_recommendations
            
            # 体质判断
            constitution = self._determine_constitution_type(syndrome_result)
            tcm_analysis["constitution_type"] = constitution
            
            # 健康建议
            suggestions = self._generate_health_suggestions(syndrome_result, constitution)
            tcm_analysis["health_suggestions"] = suggestions
            
            # 计算置信度
            tcm_analysis["confidence_score"] = self._calculate_tcm_confidence(face_analysis)
            
            print(f"✅ [TCM-AI] 中医面诊分析完成，置信度: {tcm_analysis['confidence_score']:.2f}")
            return tcm_analysis
            
        except Exception as e:
            print(f"❌ [TCM-AI] 中医面诊分析失败: {e}")
            traceback.print_exc()
            return self._get_fallback_tcm_analysis()
    
    def _analyze_face_color(self, face_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析面色"""
        try:
            # 从面部分析中提取颜色信息
            skin_health = face_analysis.get("skin_health_score", 50)
            brightness = face_analysis.get("brightness", 128)
            saturation = face_analysis.get("saturation", 128)
            
            # 判断面色类型
            if brightness < 100:
                color_type = "dark"
            elif brightness > 180:
                color_type = "red" 
            elif saturation < 80:
                color_type = "pale"
            else:
                color_type = "yellow"
            
            # 获取对应的中医诊断
            diagnosis = self.facial_diagnosis_knowledge["face_color"].get(
                color_type, 
                {"syndrome": "正常", "description": "面色正常", "herbs": []}
            )
            
            return {
                "color_type": color_type,
                "syndrome": diagnosis["syndrome"],
                "description": diagnosis["description"],
                "recommended_herbs": diagnosis["herbs"],
                "confidence": min(1.0, abs(brightness - 128) / 128)
            }
            
        except Exception as e:
            print(f"❌ [TCM-AI] 面色分析失败: {e}")
            return {"color_type": "normal", "syndrome": "正常", "description": "面色正常"}
    
    def _analyze_eye_features(self, face_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析眼部特征"""
        try:
            # 模拟眼部特征分析
            eye_features = []
            
            # 基于健康评分判断眼部状态
            health_score = face_analysis.get("health_score", 70)
            
            if health_score < 60:
                eye_features.append("dark_circles")
            if health_score < 50:
                eye_features.append("puffy")
            if health_score < 40:
                eye_features.append("dry")
            
            # 获取对应的中医诊断
            eye_diagnosis = []
            for feature in eye_features:
                diagnosis = self.facial_diagnosis_knowledge["eye_features"].get(feature, {})
                if diagnosis:
                    eye_diagnosis.append(diagnosis)
            
            return {
                "detected_features": eye_features,
                "diagnoses": eye_diagnosis,
                "primary_syndrome": eye_diagnosis[0]["syndrome"] if eye_diagnosis else "正常",
                "confidence": len(eye_features) * 0.3
            }
            
        except Exception as e:
            print(f"❌ [TCM-AI] 眼部分析失败: {e}")
            return {"detected_features": [], "diagnoses": [], "primary_syndrome": "正常"}
    
    def _analyze_lip_features(self, face_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析唇部特征"""
        try:
            # 模拟唇部特征分析
            lip_features = []
            
            # 基于面部分析判断唇部状态
            skin_health = face_analysis.get("skin_health_score", 70)
            
            if skin_health < 60:
                lip_features.append("pale")
            if skin_health < 40:
                lip_features.append("dry")
            
            # 获取对应的中医诊断
            lip_diagnosis = []
            for feature in lip_features:
                diagnosis = self.facial_diagnosis_knowledge["lip_features"].get(feature, {})
                if diagnosis:
                    lip_diagnosis.append(diagnosis)
            
            return {
                "detected_features": lip_features,
                "diagnoses": lip_diagnosis,
                "primary_syndrome": lip_diagnosis[0]["syndrome"] if lip_diagnosis else "正常",
                "confidence": len(lip_features) * 0.3
            }
            
        except Exception as e:
            print(f"❌ [TCM-AI] 唇部分析失败: {e}")
            return {"detected_features": [], "diagnoses": [], "primary_syndrome": "正常"}
    
    def _comprehensive_syndrome_analysis(self, face_color, eye_features, lip_features) -> Dict[str, Any]:
        """综合证候分析"""
        try:
            # 收集所有证候
            syndromes = []
            
            if face_color.get("syndrome") != "正常":
                syndromes.append(face_color["syndrome"])
            
            if eye_features.get("primary_syndrome") != "正常":
                syndromes.append(eye_features["primary_syndrome"])
                
            if lip_features.get("primary_syndrome") != "正常":
                syndromes.append(lip_features["primary_syndrome"])
            
            # 统计证候频次
            syndrome_counts = {}
            for syndrome in syndromes:
                syndrome_counts[syndrome] = syndrome_counts.get(syndrome, 0) + 1
            
            # 确定主要证候
            if syndrome_counts:
                primary_syndrome = max(syndrome_counts, key=syndrome_counts.get)
                confidence = syndrome_counts[primary_syndrome] / len(syndromes)
            else:
                primary_syndrome = "平和证"
                confidence = 0.8
            
            return {
                "primary_syndrome": primary_syndrome,
                "all_syndromes": list(syndrome_counts.keys()),
                "syndrome_confidence": confidence,
                "analysis_summary": f"主要证候为{primary_syndrome}，建议针对性调理"
            }
            
        except Exception as e:
            print(f"❌ [TCM-AI] 综合证候分析失败: {e}")
            return {"primary_syndrome": "平和证", "syndrome_confidence": 0.5}
    
    def _generate_herb_recommendations(self, syndrome_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成中药推荐"""
        try:
            recommendations = []
            
            # 基于主要证候推荐中药
            primary_syndrome = syndrome_analysis.get("primary_syndrome", "平和证")
            
            # 从内置知识库中查找相关中药
            if 'medicinal_material' in self.entities:
                herbs_df = self.entities['medicinal_material']
                
                # 根据证候类型推荐中药
                if "虚" in primary_syndrome:
                    recommended_herbs = herbs_df[herbs_df['property'].isin(['温', '平'])].head(3)
                elif "热" in primary_syndrome:
                    recommended_herbs = herbs_df[herbs_df['property'].isin(['寒', '凉'])].head(3) if 'property' in herbs_df.columns else herbs_df.head(3)
                else:
                    recommended_herbs = herbs_df.head(3)
                
                for _, herb in recommended_herbs.iterrows():
                    recommendations.append({
                        "name": herb.get('chinese_name', '未知'),
                        "english_name": herb.get('english_name', ''),
                        "property": herb.get('property', ''),
                        "flavour": herb.get('flavour', ''),
                        "tropism": herb.get('tropism', ''),
                        "usage": "请咨询中医师后使用",
                        "confidence": 0.7
                    })
            
            return recommendations[:5]  # 最多返回5个推荐
            
        except Exception as e:
            print(f"❌ [TCM-AI] 中药推荐生成失败: {e}")
            return []
    
    def _determine_constitution_type(self, syndrome_analysis: Dict[str, Any]) -> str:
        """判断体质类型"""
        try:
            primary_syndrome = syndrome_analysis.get("primary_syndrome", "平和证")
            
            # 基于证候判断体质
            constitution_mapping = {
                "气虚证": "气虚质",
                "血虚证": "血虚质", 
                "阴虚证": "阴虚质",
                "阳虚证": "阳虚质",
                "湿热证": "湿热质",
                "痰湿证": "痰湿质",
                "血瘀证": "血瘀质",
                "气郁证": "气郁质",
                "特禀证": "特禀质"
            }
            
            return constitution_mapping.get(primary_syndrome, "平和质")
            
        except Exception as e:
            print(f"❌ [TCM-AI] 体质判断失败: {e}")
            return "平和质"
    
    def _generate_health_suggestions(self, syndrome_analysis: Dict[str, Any], constitution: str) -> List[str]:
        """生成健康建议"""
        try:
            suggestions = []
            
            primary_syndrome = syndrome_analysis.get("primary_syndrome", "平和证")
            
            # 基于证候和体质生成建议
            if "虚" in primary_syndrome:
                suggestions.extend([
                    "注意休息，避免过度劳累",
                    "适当进补，可食用温补食物",
                    "保持规律作息，早睡早起"
                ])
            elif "热" in primary_syndrome:
                suggestions.extend([
                    "清淡饮食，多食蔬菜水果",
                    "避免辛辣刺激食物",
                    "保持心情舒畅，避免急躁"
                ])
            else:
                suggestions.extend([
                    "保持均衡饮食",
                    "适量运动，增强体质",
                    "定期体检，关注健康"
                ])
            
            # 基于体质添加特定建议
            if constitution == "气虚质":
                suggestions.append("可适当食用人参、黄芪等补气食材")
            elif constitution == "阴虚质":
                suggestions.append("多食滋阴润燥食物，如银耳、百合")
            
            return suggestions[:5]  # 最多返回5条建议
            
        except Exception as e:
            print(f"❌ [TCM-AI] 健康建议生成失败: {e}")
            return ["保持健康生活方式"]
    
    def _calculate_tcm_confidence(self, face_analysis: Dict[str, Any]) -> float:
        """计算TCM分析置信度"""
        try:
            # 基于面部分析质量计算置信度
            base_confidence = 0.6
            
            # 如果有皮肤健康评分，增加置信度
            if "skin_health_score" in face_analysis:
                score_factor = abs(face_analysis["skin_health_score"] - 50) / 50
                base_confidence += score_factor * 0.2
            
            # 如果有多个分析指标，增加置信度
            analysis_count = len([k for k in face_analysis.keys() if k in ["brightness", "saturation", "health_score"]])
            base_confidence += analysis_count * 0.05
            
            return min(0.95, base_confidence)
            
        except Exception as e:
            print(f"❌ [TCM-AI] 置信度计算失败: {e}")
            return 0.6
    
    def _get_fallback_tcm_analysis(self) -> Dict[str, Any]:
        """获取备用TCM分析结果"""
        return {
            "timestamp": datetime.now().isoformat(),
            "tcm_diagnosis": {
                "face_color": {"color_type": "normal", "syndrome": "正常"},
                "eye_features": {"primary_syndrome": "正常"},
                "lip_features": {"primary_syndrome": "正常"}
            },
            "syndrome_analysis": {
                "primary_syndrome": "平和证",
                "syndrome_confidence": 0.5,
                "analysis_summary": "面部特征基本正常，建议保持健康生活方式"
            },
            "herb_recommendations": [],
            "constitution_type": "平和质",
            "health_suggestions": ["保持均衡饮食", "适量运动", "规律作息"],
            "confidence_score": 0.5
        }
    
    def get_tcm_knowledge_status(self) -> Dict[str, Any]:
        """获取TCM知识图谱状态"""
        return {
            "knowledge_loaded": self.tcm_knowledge_loaded,
            "entities_count": len(self.entities),
            "relations_count": len(self.relations),
            "sentence_model_available": self.sentence_model is not None,
            "project_dir": str(self.project_dir),
            "last_update": datetime.now().isoformat()
        }


class TCMHealthIntegration:
    """TCM健康监测集成器"""
    
    def __init__(self):
        self.tcm_analyzer = TCMKnowledgeGraphAnalyzer()
        self.integration_enabled = True
        print("✅ [TCM-Integration] TCM健康监测集成器初始化完成")
    
    def enhance_health_analysis(self, face_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """增强健康分析，集成TCM知识"""
        try:
            if not self.integration_enabled:
                return face_analysis
            
            print("🔄 [TCM-Integration] 开始增强健康分析...")
            
            # 执行TCM面诊分析
            tcm_analysis = self.tcm_analyzer.analyze_facial_features_tcm(face_analysis)
            
            # 将TCM分析结果融合到原有分析中
            enhanced_analysis = face_analysis.copy()
            enhanced_analysis["tcm_analysis"] = tcm_analysis
            
            # 更新综合健康评分
            if "health_score" in enhanced_analysis:
                tcm_confidence = tcm_analysis.get("confidence_score", 0.5)
                original_score = enhanced_analysis["health_score"]
                
                # 基于TCM分析调整健康评分
                if tcm_analysis["syndrome_analysis"]["primary_syndrome"] == "平和证":
                    adjustment = 5  # 平和证加分
                elif "虚" in tcm_analysis["syndrome_analysis"]["primary_syndrome"]:
                    adjustment = -10  # 虚证减分
                else:
                    adjustment = 0
                
                enhanced_analysis["tcm_enhanced_health_score"] = max(0, min(100, original_score + adjustment))
            
            print("✅ [TCM-Integration] 健康分析增强完成")
            return enhanced_analysis
            
        except Exception as e:
            print(f"❌ [TCM-Integration] 健康分析增强失败: {e}")
            return face_analysis
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        try:
            tcm_status = self.tcm_analyzer.get_tcm_knowledge_status()
            return {
                "integration_enabled": self.integration_enabled,
                "knowledge_graph_loaded": tcm_status.get("knowledge_loaded", False),
                "entity_types_count": tcm_status.get("entity_types", 0),
                "tcm_analyzer_status": tcm_status
            }
        except Exception as e:
            print(f"⚠️ [TCM-Integration] 获取状态异常: {e}")
            return {
                "integration_enabled": self.integration_enabled,
                "knowledge_graph_loaded": False,
                "entity_types_count": 0,
                "tcm_analyzer_status": {}
            }


# 全局集成器实例
tcm_health_integration = None

def get_tcm_integration():
    """获取TCM集成器实例"""
    global tcm_health_integration
    if tcm_health_integration is None:
        tcm_health_integration = TCMHealthIntegration()
    return tcm_health_integration


def test_tcm_integration():
    """测试TCM集成功能"""
    print("🧪 [TCM-Integration] 开始测试TCM集成功能...")
    
    try:
        # 创建集成器
        integration = TCMHealthIntegration()
        
        # 模拟面部分析数据
        test_face_analysis = {
            "skin_health_score": 65,
            "health_score": 70,
            "brightness": 120,
            "saturation": 90,
            "timestamp": datetime.now().isoformat()
        }
        
        # 执行增强分析
        enhanced_result = integration.enhance_health_analysis(test_face_analysis)
        
        print("✅ [TCM-Integration] 测试完成")
        print(f"原始分析键数: {len(test_face_analysis)}")
        print(f"增强分析键数: {len(enhanced_result)}")
        
        if "tcm_analysis" in enhanced_result:
            tcm_result = enhanced_result["tcm_analysis"]
            print(f"TCM分析结果: {tcm_result['syndrome_analysis']['primary_syndrome']}")
            print(f"体质类型: {tcm_result['constitution_type']}")
            print(f"置信度: {tcm_result['confidence_score']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ [TCM-Integration] 测试失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 运行测试
    test_tcm_integration()
