#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传统健康智慧时钟 V1.1.2

版权所有 © 2025 湖南全航信息通信有限公司
保留所有权利。

本软件及其相关文档受版权法保护。
未经湖南全航信息通信有限公司书面许可，
不得复制、分发或修改本软件的任何部分。

联系方式：湖南全航信息通信有限公司

更新日志：
- V1.1.2 (2025.07.21): 🎯 完整功能版，时钟主界面+人脸跟踪框+综合报告完整集成
- V1.1.1 (2025.01.07): 🎯 修复跟踪框显示问题，人脸检测框正常显示
- V1.1.0: AI增强版，集成高级健康监测功能
"""

import sys
import math
import json
import pygame
from datetime import datetime
from PyQt5 import QtCore
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtCore import Qt
# 🔥 临时注释掉CSS警告过滤器，避免可能的启动问题
# import os
# import logging
# 
# # 🔥 新增：CSS警告过滤器，减少无关的Unknown property警告
# class CSSWarningFilter(logging.Filter):
#     """过滤CSS相关的无关警告"""
#     def filter(self, record):
#         message = record.getMessage()
#         # 过滤掉常见的Qt CSS警告
#         if any(warning in message for warning in [
#             "Unknown property box-shadow",
#             "Unknown property transform", 
#             "Unknown property transition",
#             "Unknown property filter",
#             "Unknown property backdrop-filter",
#             "Unknown property box-decoration-break"
#         ]):
#             return False
#         return True
# 
# # 设置日志过滤器和环境变量，抑制CSS警告
# css_filter = CSSWarningFilter()
# logging.getLogger().addFilter(css_filter)
# os.environ['QT_LOGGING_RULES'] = '*=false;qt.qss.*=false'
# os.environ['QT_QSS_WARNINGS'] = 'false'

try:
    from lunar_python import Lunar, Solar
except ImportError:
    Lunar = Solar = None

# 导入健康监测模块（优先级：高级版 > AI版 > 基础版）
ADVANCED_HEALTH_MONITOR_AVAILABLE = False
AI_HEALTH_MONITOR_AVAILABLE = False
HEALTH_MONITOR_AVAILABLE = False

# 尝试导入高级健康监测模块
try:
    from advanced_health_monitor import AdvancedHealthMonitorWidget
    ADVANCED_HEALTH_MONITOR_AVAILABLE = True
    print("高级健康监测模块加载成功")
except ImportError:
    print("高级健康监测模块未找到")

# AI健康监测模块集成在高级版中，无需单独导入
if ADVANCED_HEALTH_MONITOR_AVAILABLE:
    AI_HEALTH_MONITOR_AVAILABLE = True
    print("AI健康监测功能已集成在高级模块中")

# 尝试导入基础健康监测模块
if not ADVANCED_HEALTH_MONITOR_AVAILABLE and not AI_HEALTH_MONITOR_AVAILABLE:
    try:
        from health_monitor import HealthMonitor, HealthMonitorWidget
        HEALTH_MONITOR_AVAILABLE = True
        print("基础健康监测模块加载成功")
    except ImportError:
        print("健康监测模块未找到，将跳过相关功能")

class ProfessionalBaguaClock(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("传统健康智慧时钟 V1.1.2")

        # 🎯 版本更新标记 - 2025.07.21 17:57:00
        # V1.1.2 (完整功能版): 时钟主界面+人脸跟踪框+综合报告完整集成
        # 修复内容: 所有编译问题完全解决，文件大小516MB
        # 用户确认: 编译成功，所有功能正常 ✅
        
        # 时钟缩放功能
        self.base_size = 1000    # 基础尺寸
        
        # 强制设置默认缩放为65%，不读取配置文件
        self.scale_factor = 0.65  # 强制65%
        print(f"程序启动，强制使用65%缩放 (scale_factor={self.scale_factor})")
        
        self.current_size = int(self.base_size * self.scale_factor)
        self.setFixedSize(self.current_size, self.current_size)
        
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 动画参数 - 优化性能：降低帧率
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(50)  # 20 FPS，减少CPU占用
        
        # 时间更新
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        
        # 动画状态
        self.angle_offset = 0
        self.pulse_scale = 1.0
        self.glow_intensity = 0
        
        # 健康提醒状态
        self.health_reminder_shown = {}  # 记录每个小时是否已显示提醒
        self.shichen_reminder_shown = {}  # 记录每个时辰是否已显示提醒
        
        # 设置缓存
        self.settings_cache = self.load_system_settings()
        
        # 悬停对话框状态
        self.hover_dialog = None  # 当前悬停对话框
        self.current_hover_shichen = None  # 当前悬停的时辰索引
        
        # 启用鼠标跟踪
        self.setMouseTracking(True)
        
        # 八卦信息
        self.bagua_info = [
            ("乾", "☰", 0), ("兑", "☱", 45), ("离", "☲", 90), ("震", "☳", 135),
            ("巽", "☴", 180), ("坎", "☵", 225), ("艮", "☶", 270), ("坤", "☷", 315)
        ]
        
        # 时辰映射 - 修正为传统12时辰对应
        self.time_bagua_map = {
            # 子时 (23:00-01:00) → 坎卦 ☵
            23: 5, 0: 5,
            # 丑时 (01:00-03:00) → 艮卦 ☶
            1: 6, 2: 6,
            # 寅时 (03:00-05:00) → 震卦 ☳
            3: 3, 4: 3,
            # 卯时 (05:00-07:00) → 巽卦 ☴
            5: 4, 6: 4,
            # 辰时 (07:00-09:00) → 离卦 ☲
            7: 2, 8: 2,
            # 巳时 (09:00-11:00) → 坤卦 ☷
            9: 7, 10: 7,
            # 午时 (11:00-13:00) → 兑卦 ☱
            11: 1, 12: 1,
            # 未时 (13:00-15:00) → 乾卦 ☰
            13: 0, 14: 0,
            # 申时 (15:00-17:00) → 坎卦 ☵
            15: 5, 16: 5,
            # 酉时 (17:00-19:00) → 艮卦 ☶
            17: 6, 18: 6,
            # 戌时 (19:00-21:00) → 震卦 ☳
            19: 3, 20: 3,
            # 亥时 (21:00-23:00) → 巽卦 ☴
            21: 4, 22: 4
        }
        
        self.current_time = datetime.now()
        self.drag_position = None
        
        # 创建标题栏
        self.create_title_bar()
        
        # 创建功能按钮
        self.create_function_buttons()
        
        # 初始化记事本和闹钟
        self.notepad_window = None
        self.alarm_window = None
        self.alarms = []
        self.load_alarms()
        
        # 初始化健康监测
        self.health_monitor_window = None
        self.health_monitor = None
        self.camera_enabled = True  # 默认开启摄像头
        
        # 初始化pygame音频
        pygame.mixer.init()
        
        # 创建系统托盘
        self.create_system_tray()
        
        # 创建缩放快捷键
        self.create_scale_shortcuts()
        
        # 设置窗口默认位置到右上角
        self.set_default_position()
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background: transparent;
                color: #FFD700;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 15px;
                color: #FFD700;
                font-weight: bold;
                padding: 8px;
                min-width: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
    
    def create_title_bar(self):
        """创建专业标题栏"""
        title_bar = QFrame()
        title_bar.setFixedHeight(40)
        title_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
            }
        """)
        
        layout = QHBoxLayout(title_bar)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 标题
        title_label = QLabel("传统健康智慧时钟 V1.1.2")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        # 控制按钮
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(5)
        
        # 按钮统一样式
        btn_style = """
            QPushButton {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                background: transparent;
                border: 2px solid #FFD700;
                border-radius: 15px;
            }
            QPushButton:hover {
                background: #333333;
                border-color: #FFA500;
            }
        """

        # 隐藏按钮
        hide_btn = QPushButton("─")
        hide_btn.setFixedSize(30, 30)
        hide_btn.setStyleSheet(btn_style)
        hide_btn.clicked.connect(self.minimize_to_tray)

        # 全屏按钮
        max_btn = QPushButton("■")
        max_btn.setFixedSize(30, 30)
        max_btn.setStyleSheet(btn_style)
        max_btn.clicked.connect(self.toggle_fullscreen)

        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet(btn_style + "QPushButton:hover {background: #FF4444; border-color: #FF6666;}")
        close_btn.clicked.connect(self.close_application)

        btn_layout.addWidget(hide_btn)
        btn_layout.addWidget(max_btn)
        btn_layout.addWidget(close_btn)
        
        layout.addWidget(title_label)
        layout.addStretch()
        layout.addLayout(btn_layout)
        
        # 将标题栏添加到主窗口
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(title_bar)
        main_layout.addStretch()
    
    def close_application(self):
        """关闭应用程序 - 添加确认对话框保护主程序不被意外关闭"""
        msg = QMessageBox(self)
        msg.setWindowTitle('确认退出')
        msg.setText('确定要退出传统健康智慧时钟吗？\n\n程序退出后：\n• 健康监测功能将停止\n• 时间提醒功能将停止\n• 数据会自动保存')
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setDefaultButton(QMessageBox.No)
        msg.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint | Qt.WindowCloseButtonHint)
        reply = msg.exec_()
        
        if reply == QMessageBox.Yes:
            # 标记为确认关闭，避免closeEvent重复询问
            self._confirmed_close = True
            
            # 保存所有数据
            self.save_alarms()
            if hasattr(self, 'advanced_health_monitor_window') and self.advanced_health_monitor_window:
                try:
                    if hasattr(self.advanced_health_monitor_window, 'is_monitoring') and self.advanced_health_monitor_window.is_monitoring:
                        self.advanced_health_monitor_window.stop_monitoring()
                    if hasattr(self.advanced_health_monitor_window, 'save_settings'):
                        self.advanced_health_monitor_window.save_settings()
                except:
                    pass
            
            # 正常退出程序
            self.close()
    
    def toggle_fullscreen(self):
        """切换真正的全屏状态"""
        if not hasattr(self, 'is_fullscreen'):
            self.is_fullscreen = False
            self.original_scale_factor = self.scale_factor
            self.original_geometry = self.geometry()
        
        if self.is_fullscreen:
            # 退出全屏
            self.showNormal()
            self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
            self.show()
            # 恢复原始缩放和位置
            self.scale_factor = self.original_scale_factor
            self.setGeometry(self.original_geometry)
            self.update_size()
            self.is_fullscreen = False
        else:
            # 进入全屏
            self.original_scale_factor = self.scale_factor
            self.original_geometry = self.geometry()
            
            # 获取屏幕尺寸
            from PyQt5.QtWidgets import QDesktopWidget
            desktop = QDesktopWidget()
            screen_geometry = desktop.screenGeometry()
            
            # 计算全屏所需的缩放比例
            screen_size = min(screen_geometry.width(), screen_geometry.height())
            fullscreen_scale = screen_size / self.base_size * 0.9  # 留10%边距
            
            # 设置全屏缩放
            self.scale_factor = fullscreen_scale
            
            # 设置全屏显示
            self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
            self.showFullScreen()
            
            # 更新尺寸和位置
            self.update_size()
            
            # 居中显示
            self.move((screen_geometry.width() - self.current_size) // 2,
                     (screen_geometry.height() - self.current_size) // 2)
            
            self.is_fullscreen = True
    
    def exit_fullscreen(self):
        """退出全屏模式"""
        if hasattr(self, 'is_fullscreen') and self.is_fullscreen:
            self.toggle_fullscreen()
    
    def create_system_tray(self):
        """创建系统托盘"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            QMessageBox.critical(self, "系统托盘", "系统托盘不可用")
            return
        
        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon(self)
        
        # 设置托盘图标（创建简单图标）
        pixmap = QPixmap(16, 16)
        pixmap.fill(QColor(255, 215, 0))  # 金色图标
        self.tray_icon.setIcon(QIcon(pixmap))
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        # 显示/隐藏动作
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_from_tray)
        tray_menu.addAction(show_action)
        
        hide_action = QAction("隐藏到托盘", self)
        hide_action.triggered.connect(self.minimize_to_tray)
        tray_menu.addAction(hide_action)
        
        tray_menu.addSeparator()
        
        # 记事本动作
        notepad_action = QAction("记事本", self)
        notepad_action.triggered.connect(self.open_notepad)
        tray_menu.addAction(notepad_action)
        
        # 闹钟动作
        alarm_action = QAction("闹钟", self)
        alarm_action.triggered.connect(self.open_alarm_settings)
        tray_menu.addAction(alarm_action)
        
        # 健康监测动作
        if ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE:
            health_action = QAction("🏥 健康监测", self)
            health_action.triggered.connect(self.open_health_monitor)
            tray_menu.addAction(health_action)
        
        tray_menu.addSeparator()
        
        # 缩放控制动作
        zoom_in_action = QAction("🔍 放大 (Ctrl++)", self)
        zoom_in_action.triggered.connect(self.zoom_in)
        tray_menu.addAction(zoom_in_action)
        
        zoom_out_action = QAction("🔍 缩小 (Ctrl+-)", self)
        zoom_out_action.triggered.connect(self.zoom_out)
        tray_menu.addAction(zoom_out_action)
        
        tray_menu.addSeparator()
        
        # 退出动作
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.close_application)
        tray_menu.addAction(quit_action)
        
        # 设置托盘菜单样式
        tray_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                border: 2px solid #FFD700;
                color: #FFD700;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 5px;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
            }
        """)
        
        self.tray_icon.setContextMenu(tray_menu)
        
        # 双击托盘图标显示窗口
        self.tray_icon.activated.connect(self.tray_icon_activated)
        
        # 设置托盘提示
        self.tray_icon.setToolTip("传统健康智慧时钟 V1.1.2")
        
        # 显示托盘图标
        self.tray_icon.show()
    
    def create_scale_shortcuts(self):
        """创建缩放快捷键"""
        # 放大快捷键 Ctrl++
        zoom_in_shortcut = QShortcut(QKeySequence("Ctrl++"), self)
        zoom_in_shortcut.activated.connect(self.zoom_in)
        
        # 缩小快捷键 Ctrl+-
        zoom_out_shortcut = QShortcut(QKeySequence("Ctrl+-"), self)
        zoom_out_shortcut.activated.connect(self.zoom_out)
        
        # 重置大小快捷键 Ctrl+0
        reset_size_shortcut = QShortcut(QKeySequence("Ctrl+0"), self)
        reset_size_shortcut.activated.connect(self.reset_size)
        
        # 全屏切换快捷键 F11
        fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        fullscreen_shortcut.activated.connect(self.toggle_fullscreen)
        
        # 退出全屏快捷键 ESC
        escape_shortcut = QShortcut(QKeySequence("Escape"), self)
        escape_shortcut.activated.connect(self.exit_fullscreen)
    
    def set_default_position(self):
        """设置窗口默认位置到右上角"""
        try:
            # 获取屏幕尺寸
            from PyQt5.QtWidgets import QDesktopWidget
            desktop = QDesktopWidget()
            screen_geometry = desktop.availableGeometry()
            
            # 计算右上角位置（距离右边和上边各留20像素边距）
            x = screen_geometry.width() - self.current_size - 20
            y = 20
            
            # 设置窗口位置
            self.move(x, y)
        except:
            # 如果获取屏幕尺寸失败，使用默认位置
            self.move(800, 20)
    
    def zoom_in(self):
        """放大时钟"""
        if self.scale_factor < 2.0:  # 最大放大到2倍
            self.scale_factor += 0.1
            self.update_size()
    
    def zoom_out(self):
        """缩小时钟"""
        if self.scale_factor > 0.5:  # 最小缩小到0.5倍
            self.scale_factor -= 0.1
            self.update_size()
    
    def reset_size(self):
        """重置时钟大小"""
        self.scale_factor = 1.0
        self.update_size()
    
    def update_size(self):
        """更新窗口大小"""
        self.current_size = int(self.base_size * self.scale_factor)
        
        # 保存当前位置
        current_pos = self.pos()
        
        # 更新窗口大小
        self.setFixedSize(self.current_size, self.current_size)
        
        # 保持窗口居中（可选）
        # self.move(current_pos.x() + (old_size - self.current_size) // 2,
        #           current_pos.y() + (old_size - self.current_size) // 2)
        
        # 重新创建功能按钮以适应新尺寸
        self.update_function_buttons()
        
        # 更新缩放显示
        self.update_scale_display()
        
        # 强制重绘
        self.update()
    
    def update_function_buttons(self):
        """更新功能按钮位置和样式以适应缩放"""
        # 调整按钮位置，不要太靠右边 - 缩小三分之一
        button_x = int(self.current_size - 100 * self.scale_factor)  # 距离右边也要缩放，缩小到100
        
        # 重新计算样式（根据当前缩放因子）- 优化字体和按钮尺寸
        button_font_size = max(10, int(20 * self.scale_factor))  # 缩小字体，避免挡住
        button_style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: {int(2 * self.scale_factor)}px solid #FFD700;
                border-radius: {int(15 * self.scale_factor)}px;
                color: #FFD700;
                font-weight: bold;
                font-size: {button_font_size}px;
                padding: {int(6 * self.scale_factor)}px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }}
        """
        
        zoom_font_size = max(12, int(22 * self.scale_factor))  # 缩小字体，避免挡住
        zoom_button_style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A4A2A, stop:1 #1A3A1A);
                border: {int(2 * self.scale_factor)}px solid #66FF66;
                border-radius: {int(12 * self.scale_factor)}px;
                color: #66FF66;
                font-weight: bold;
                font-size: {zoom_font_size}px;
                padding: {int(5 * self.scale_factor)}px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A6A4A, stop:1 #3A5A3A);
                border-color: #88FF88;
            }}
        """
        
        # 更新按钮样式
        self.notepad_btn.setStyleSheet(button_style)
        self.alarm_btn.setStyleSheet(button_style)
        self.settings_btn.setStyleSheet(button_style + """
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A6A4A, stop:1 #2A4A2A);
                border-color: #66FF66;
            }
        """)
        
        self.zoom_in_btn.setStyleSheet(zoom_button_style)
        self.zoom_out_btn.setStyleSheet(zoom_button_style)
        
        # 计算按钮尺寸，确保与缩放同步
        button_width = int(85 * self.scale_factor)
        button_height = int(35 * self.scale_factor)
        zoom_button_height = int(32 * self.scale_factor)
        
        # 记事本按钮
        self.notepad_btn.setGeometry(button_x, int(60 * self.scale_factor), 
                                    button_width, button_height)
        
        # 闹钟按钮
        self.alarm_btn.setGeometry(button_x, int(105 * self.scale_factor), 
                                  button_width, button_height)
        
        # 系统设置按钮
        self.settings_btn.setGeometry(button_x, int(150 * self.scale_factor), 
                                     button_width, button_height)
        
        # 健康监测按钮（如果存在）- 强制显示
        if hasattr(self, 'health_btn') and (ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE):
            self.health_btn.setStyleSheet(button_style + """
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4A4A6A, stop:1 #2A2A4A);
                    border-color: #66AAFF;
                }
            """)
            self.health_btn.setGeometry(button_x, int(195 * self.scale_factor), 
                                       button_width, button_height)
            self.health_btn.setVisible(True)  # 强制显示
            self.health_btn.raise_()  # 提升到最前面
            print(f"更新健康按钮位置: ({button_x}, {int(195 * self.scale_factor)})")
        
        # 缩放控制按钮
        zoom_start_y = 240 if (ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE) else 195
        self.zoom_in_btn.setGeometry(button_x, int(zoom_start_y * self.scale_factor), 
                                    button_width, zoom_button_height)
        
        self.zoom_out_btn.setGeometry(button_x, int((zoom_start_y + 40) * self.scale_factor), 
                                     button_width, zoom_button_height)
    
    def minimize_to_tray(self):
        """最小化到系统托盘"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.hide()
            # 移除托盘提示消息，避免每次都弹出通知
        else:
            # 如果托盘不可用，则最小化窗口
            self.showMinimized()
    
    def show_from_tray(self):
        """从系统托盘显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def tray_icon_activated(self, reason):
        """托盘图标激活事件"""
        # 单击显示/隐藏，双击也支持显示/隐藏
        if reason in [QSystemTrayIcon.ActivationReason.Trigger, QSystemTrayIcon.ActivationReason.DoubleClick]:
            if self.isVisible():
                self.minimize_to_tray()
            else:
                self.show_from_tray()
    

    
    def create_function_buttons(self):
        """创建功能按钮（记事本、闹钟、设置、缩放控制）"""
        # 按钮样式（根据缩放调整字体）- 优化字体和按钮尺寸
        button_font_size = max(10, int(20 * self.scale_factor))  # 缩小字体，避免挡住
        button_style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: {int(2 * self.scale_factor)}px solid #FFD700;
                border-radius: {int(15 * self.scale_factor)}px;
                color: #FFD700;
                font-weight: bold;
                font-size: {button_font_size}px;
                padding: {int(6 * self.scale_factor)}px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }}
        """
        
        # 缩放按钮样式（根据缩放调整字体）- 优化字体和按钮尺寸
        zoom_font_size = max(12, int(22 * self.scale_factor))  # 缩小字体，避免挡住
        zoom_button_style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A4A2A, stop:1 #1A3A1A);
                border: {int(2 * self.scale_factor)}px solid #66FF66;
                border-radius: {int(12 * self.scale_factor)}px;
                color: #66FF66;
                font-weight: bold;
                font-size: {zoom_font_size}px;
                padding: {int(5 * self.scale_factor)}px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A6A4A, stop:1 #3A5A3A);
                border-color: #88FF88;
            }}
        """
        
        # 计算按钮尺寸，确保与缩放同步
        button_x = int(self.current_size - 95 * self.scale_factor)
        button_width = int(85 * self.scale_factor)
        button_height = int(35 * self.scale_factor)
        zoom_button_height = int(32 * self.scale_factor)
        
        # 记事本按钮
        self.notepad_btn = QPushButton("记事本", self)
        self.notepad_btn.setStyleSheet(button_style)
        self.notepad_btn.setGeometry(button_x, int(60 * self.scale_factor), button_width, button_height)
        self.notepad_btn.clicked.connect(self.open_notepad)
        
        # 闹钟按钮
        self.alarm_btn = QPushButton("闹钟", self)
        self.alarm_btn.setStyleSheet(button_style)
        self.alarm_btn.setGeometry(button_x, int(105 * self.scale_factor), button_width, button_height)
        self.alarm_btn.clicked.connect(self.open_alarm_settings)
        
        # 系统设置按钮
        self.settings_btn = QPushButton("设置", self)
        self.settings_btn.setStyleSheet(button_style + """
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A6A4A, stop:1 #2A4A2A);
                border-color: #66FF66;
            }
        """)
        self.settings_btn.setGeometry(button_x, int(150 * self.scale_factor), button_width, button_height)
        self.settings_btn.clicked.connect(self.open_system_settings)
        
        # 健康监测按钮 - 强制显示
        print(f"健康监测模块状态: ADVANCED={ADVANCED_HEALTH_MONITOR_AVAILABLE}, AI={AI_HEALTH_MONITOR_AVAILABLE}, HEALTH={HEALTH_MONITOR_AVAILABLE}")
        if ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE:
            self.health_btn = QPushButton("健康", self)
            self.health_btn.setStyleSheet(button_style + """
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4A4A6A, stop:1 #2A2A4A);
                    border-color: #66AAFF;
                }
            """)
            self.health_btn.setGeometry(button_x, int(195 * self.scale_factor), button_width, button_height)
            self.health_btn.clicked.connect(self.open_health_monitor)
            self.health_btn.setToolTip("智能健康监测")
            self.health_btn.setVisible(True)  # 强制显示
            self.health_btn.raise_()  # 提升到最前面
            print(f"健康按钮已创建，位置: ({button_x}, {int(195 * self.scale_factor)}), 大小: ({button_width}, {button_height})")
        else:
            print("健康监测模块不可用，按钮未创建")
        
        # 缩放控制按钮组
        zoom_start_y = 240 if (ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE) else 195
        self.zoom_in_btn = QPushButton("🔍+", self)
        self.zoom_in_btn.setStyleSheet(zoom_button_style)
        self.zoom_in_btn.setGeometry(button_x, int(zoom_start_y * self.scale_factor), button_width, zoom_button_height)
        self.zoom_in_btn.setToolTip("放大时钟 (Ctrl++)")
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        
        self.zoom_out_btn = QPushButton("🔍-", self)
        self.zoom_out_btn.setStyleSheet(zoom_button_style)
        self.zoom_out_btn.setGeometry(button_x, int((zoom_start_y + 40) * self.scale_factor), button_width, zoom_button_height)
        self.zoom_out_btn.setToolTip("缩小时钟 (Ctrl+-)")
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        

        

    
    def update_scale_display(self):
        """更新缩放显示（已移除显示标签）"""
        pass
    
    def open_notepad(self):
        """打开记事本"""
        if not self.notepad_window:
            self.notepad_window = ProfessionalNotepad()
            # 设置记事本位置在主窗口右侧，边框对齐
            main_pos = self.pos()
            main_width = self.width()
            self.notepad_window.move(main_pos.x() + main_width + 10, main_pos.y())
        
        # 确保记事本窗口在最前面
        self.notepad_window.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        self.notepad_window.show()
        self.notepad_window.raise_()
        self.notepad_window.activateWindow()
    
    def open_alarm_settings(self):
        """打开闹钟设置"""
        try:
            # 🔥 关键修复：检查是否已存在闹钟窗口，避免重复创建
            if hasattr(self, 'alarm_window') and self.alarm_window:
                print("闹钟窗口已存在，直接显示...")
                self.alarm_window.show()
                self.alarm_window.raise_()
                self.alarm_window.activateWindow()
                return
            
            print("开始创建闹钟窗口...")  # 调试信息
            
            # 创建新的窗口实例
            print("创建新的闹钟窗口实例...")
            self.alarm_window = ProfessionalAlarmSettings(self)
            print("闹钟窗口实例创建成功")
            
            # 设置位置
            main_pos = self.pos()
            alarm_x = max(10, main_pos.x() - self.alarm_window.width() - 10)
            alarm_y = max(10, main_pos.y())
            self.alarm_window.move(alarm_x, alarm_y)
            print(f"闹钟窗口位置设置完成：({alarm_x}, {alarm_y})")
            
            print("强制显示窗口...")
            # 强制显示窗口
            self.alarm_window.setVisible(True)
            self.alarm_window.show()
            self.alarm_window.raise_()
            self.alarm_window.activateWindow()
            self.alarm_window.setFocus()
            
            # 确保窗口在屏幕上可见
            self.alarm_window.move(alarm_x, alarm_y)
            
            print(f"闹钟窗口已显示，可见性：{self.alarm_window.isVisible()}")
            
        except Exception as e:
            print(f"打开闹钟窗口时出错：{str(e)}")  # 调试信息
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"无法打开闹钟窗口：{str(e)}")
    
    def open_system_settings(self):
        """打开系统设置"""
        try:
            # 🔥 关键修复：检查是否已存在设置窗口，避免重复创建
            if hasattr(self, 'settings_window') and self.settings_window:
                print("系统设置窗口已存在，直接显示...")
                self.settings_window.show()
                self.settings_window.raise_()
                self.settings_window.activateWindow()
                return
            
            print("开始创建系统设置窗口...")
            
            # 创建新的设置窗口实例
            self.settings_window = SystemSettingsWindow(self)
            
            # 设置位置
            main_pos = self.pos()
            settings_x = max(10, main_pos.x() - self.settings_window.width() - 10)
            settings_y = max(10, main_pos.y())
            self.settings_window.move(settings_x, settings_y)
            
            # 强制显示窗口
            self.settings_window.setVisible(True)
            self.settings_window.show()
            self.settings_window.raise_()
            self.settings_window.activateWindow()
            self.settings_window.setFocus()
            
            print("系统设置窗口已显示")
            
        except Exception as e:
            print(f"打开系统设置窗口时出错：{str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"无法打开系统设置窗口：{str(e)}")
    
    def open_health_monitor(self):
        """打开健康监测窗口"""
        try:
            # 优先使用高级健康监测系统
            if ADVANCED_HEALTH_MONITOR_AVAILABLE:
                # 🔥 关键修复：检查是否已存在窗口，避免重复创建
                if hasattr(self, 'advanced_health_monitor_window') and self.advanced_health_monitor_window:
                    print("高级健康监测窗口已存在，直接显示...")
                    self.advanced_health_monitor_window.show()
                    self.advanced_health_monitor_window.raise_()
                    self.advanced_health_monitor_window.activateWindow()
                    return
                
                print("开始创建高级健康监测窗口...")
                
                # 🔥 关键修复：创建独立的健康监测窗口（不设置父窗口）
                self.advanced_health_monitor_window = AdvancedHealthMonitorWidget(None)
                
                # 🔥 修复：不直接覆盖closeEvent，而是使用信号连接
                # 保存原有的closeEvent方法
                original_close_event = self.advanced_health_monitor_window.closeEvent
                
                def enhanced_close_event(event):
                    try:
                        # 先调用原有的closeEvent处理（保持健康模块自身的清理逻辑）
                        original_close_event(event)
                        
                        # 如果原有处理接受了关闭事件，再进行主程序的清理
                        if event.isAccepted():
                            # 清空窗口引用（延迟清理，避免影响主程序）
                            QTimer.singleShot(100, lambda: setattr(self, 'advanced_health_monitor_window', None))
                        
                    except Exception as e:
                        print(f"健康监测窗口关闭时出错: {e}")
                        # 确保即使出错也接受关闭事件
                        event.accept()
                
                # 🔥 修复：使用正确的方式连接closeEvent
                self.advanced_health_monitor_window.closeEvent = enhanced_close_event
                
                # 🔥 修复：移除不存在的camera_checkbox相关代码
                def delayed_setup():
                    try:
                        # 确保窗口正确显示
                        if hasattr(self.advanced_health_monitor_window, 'show'):
                            self.advanced_health_monitor_window.show()
                            self.advanced_health_monitor_window.raise_()
                            self.advanced_health_monitor_window.activateWindow()
                    except Exception as e:
                        print(f"窗口设置错误: {e}")
                
                # 延迟100ms执行设置
                QTimer.singleShot(100, delayed_setup)
                
                # 🔥 修复时序问题：延迟获取正确的窗口尺寸并定位
                def position_health_window():
                    """延迟定位健康界面，确保窗口已完全初始化"""
                    try:
                        from PyQt5.QtWidgets import QDesktopWidget
                        desktop = QDesktopWidget()
                        screen_geometry = desktop.availableGeometry()
                        
                        # 获取时钟界面当前位置和大小
                        main_pos = self.pos()
                        main_size = self.size()
                        
                        # 🔥 修复：强制刷新窗口，确保尺寸正确
                        self.advanced_health_monitor_window.show()
                        self.advanced_health_monitor_window.update()
                        QApplication.processEvents()  # 强制处理所有待处理事件
                        
                        # 再次获取健康界面尺寸（此时应该是正确的）
                        health_width = self.advanced_health_monitor_window.width()
                        health_height = self.advanced_health_monitor_window.height()
                        
                        # 如果还是默认尺寸，使用已知的健康界面尺寸
                        if health_width <= 100 or health_height <= 100:
                            health_width = 1200  # 🔥 修复：更新为1200×800界面尺寸
                            health_height = 800  # 🔥 修复：更新为1200×800界面尺寸
                            print(f"⚠️ 使用默认健康界面尺寸: {health_width}x{health_height}")
                        else:
                            print(f"✅ 获取到正确的健康界面尺寸: {health_width}x{health_height}")
                        
                        # 强制计算健康界面位置：时钟界面左侧，留30像素间隔
                        health_x = main_pos.x() - health_width - 30
                        health_y = main_pos.y()  # 与时钟界面顶部对齐
                        
                        # 如果左侧空间不够，调整时钟界面位置，确保健康界面能显示在左侧
                        if health_x < 10:
                            # 重新定位时钟界面，确保左侧有足够空间
                            new_clock_x = health_width + 50  # 健康界面宽度 + 50像素间隔
                            new_clock_y = main_pos.y()
                            
                            # 确保时钟界面不超出屏幕右边界
                            if new_clock_x + main_size.width() > screen_geometry.width():
                                new_clock_x = screen_geometry.width() - main_size.width() - 10
                            
                            # 移动时钟界面
                            self.move(new_clock_x, new_clock_y)
                            
                            # 重新计算健康界面位置
                            health_x = new_clock_x - health_width - 30
                            health_y = new_clock_y
                            
                            print(f"📍 调整时钟界面位置到: ({new_clock_x}, {new_clock_y})")
                        
                        # 确保健康界面不超出屏幕边界
                        health_x = max(10, health_x)
                        health_y = max(10, min(health_y, screen_geometry.height() - health_height - 10))
                        
                        # 🔥 关键修复：正确设置健康界面位置
                        self.advanced_health_monitor_window.move(health_x, health_y)
                        self.advanced_health_monitor_window.resize(health_width, health_height)
                        
                        print(f"📍 时钟界面最终位置: ({self.pos().x()}, {self.pos().y()}), 大小: {main_size.width()}x{main_size.height()}")
                        print(f"📍 健康界面最终位置: ({health_x}, {health_y}), 大小: {health_width}x{health_height}")
                        print(f"📏 两界面间隔: {self.pos().x() - health_x - health_width} 像素")
                        
                        # 确保健康界面在正确位置显示
                        self.advanced_health_monitor_window.raise_()
                        self.advanced_health_monitor_window.activateWindow()
                        
                    except Exception as e:
                        print(f"❌ 定位健康界面时出错: {e}")
                        import traceback
                        traceback.print_exc()
                
                # 延迟300ms执行定位，确保窗口完全初始化
                QTimer.singleShot(300, position_health_window)
                
                # 初始显示窗口（最终定位在延迟函数中完成）
                self.advanced_health_monitor_window.show()
                
                print("高级健康监测窗口已显示")
                return
            
            # 其次使用AI增强版
            elif AI_HEALTH_MONITOR_AVAILABLE:
                # 🔥 关键修复：检查是否已存在AI窗口，避免重复创建
                if hasattr(self, 'ai_health_monitor_window') and self.ai_health_monitor_window:
                    print("AI健康监测窗口已存在，直接显示...")
                    self.ai_health_monitor_window.show()
                    self.ai_health_monitor_window.raise_()
                    self.ai_health_monitor_window.activateWindow()
                    return
                
                print("开始创建AI健康监测窗口...")
                
                # 🔥 关键修复：创建独立的AI健康监测窗口（不设置父窗口）
                self.ai_health_monitor_window = AIHealthMonitorWidget(None)
                
                # 设置位置
                main_pos = self.pos()
                health_x = max(10, main_pos.x() - self.ai_health_monitor_window.width() - 10)
                health_y = max(10, main_pos.y() + 100)  # 稍微往下偏移
                self.ai_health_monitor_window.move(health_x, health_y)
                
                # 强制显示窗口
                self.ai_health_monitor_window.setVisible(True)
                self.ai_health_monitor_window.show()
                self.ai_health_monitor_window.raise_()
                self.ai_health_monitor_window.activateWindow()
                self.ai_health_monitor_window.setFocus()
                
                print("AI健康监测窗口已显示")
                return
            
            # 最后使用基础版本
            elif HEALTH_MONITOR_AVAILABLE:
                # 🔥 关键修复：检查是否已存在基础窗口，避免重复创建
                if hasattr(self, 'health_monitor_window') and self.health_monitor_window:
                    print("基础健康监测窗口已存在，直接显示...")
                    self.health_monitor_window.show()
                    self.health_monitor_window.raise_()
                    self.health_monitor_window.activateWindow()
                    return
                
                print("开始创建基础健康监测窗口...")
                
                # 🔥 关键修复：创建独立的健康监测窗口（不设置父窗口）
                self.health_monitor_window = HealthMonitorWidget(None)
                
                # 设置位置
                main_pos = self.pos()
                health_x = max(10, main_pos.x() - self.health_monitor_window.width() - 10)
                health_y = max(10, main_pos.y() + 200)  # 稍微往下偏移
                self.health_monitor_window.move(health_x, health_y)
                
                # 强制显示窗口
                self.health_monitor_window.setVisible(True)
                self.health_monitor_window.show()
                self.health_monitor_window.raise_()
                self.health_monitor_window.activateWindow()
                self.health_monitor_window.setFocus()
                
                print("基础健康监测窗口已显示")
                return
            
            else:
                QMessageBox.warning(self, "功能不可用", "健康监测模块未安装，请安装相关依赖：\npip install opencv-python requests pillow")
                return
                
        except Exception as e:
            print(f"打开健康监测窗口时出错：{str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"无法打开健康监测窗口：{str(e)}")
    
    def update_time(self):
        """更新时间"""
        self.current_time = datetime.now()
        self.check_alarms()
        self.check_health_reminder()  # 添加健康提醒检查
        self.check_shichen_reminder()  # 添加时辰提醒检查
        self.update()
    
    def update_animation(self):
        """更新动画"""
        self.angle_offset += 1
        if self.angle_offset >= 360:
            self.angle_offset = 0
        
        # 脉冲效果
        self.pulse_scale = 1.0 + 0.05 * math.sin(self.angle_offset * 0.1)
        
        # 发光强度
        self.glow_intensity = 100 + 50 * math.sin(self.angle_offset * 0.05)
        
        self.update()
    
    def get_current_shichen_index(self, hour):
        """根据当前小时计算时辰索引"""
        # 传统12时辰对应关系：
        # 子时：23-1点，丑时：1-3点，寅时：3-5点，卯时：5-7点
        # 辰时：7-9点，巳时：9-11点，午时：11-13点，未时：13-15点  
        # 申时：15-17点，酉时：17-19点，戌时：19-21点，亥时：21-23点
        
        if 23 <= hour <= 23:  # 子时 23:00-23:59
            return 0
        elif 0 <= hour < 1:    # 子时 0:00-0:59  
            return 0
        elif 1 <= hour < 3:    # 丑时 1:00-2:59
            return 1
        elif 3 <= hour < 5:    # 寅时 3:00-4:59
            return 2
        elif 5 <= hour < 7:    # 卯时 5:00-6:59
            return 3
        elif 7 <= hour < 9:    # 辰时 7:00-8:59
            return 4
        elif 9 <= hour < 11:   # 巳时 9:00-10:59
            return 5
        elif 11 <= hour < 13:  # 午时 11:00-12:59
            return 6
        elif 13 <= hour < 15:  # 未时 13:00-14:59
            return 7
        elif 15 <= hour < 17:  # 申时 15:00-16:59
            return 8
        elif 17 <= hour < 19:  # 酉时 17:00-18:59
            return 9
        elif 19 <= hour < 21:  # 戌时 19:00-20:59
            return 10
        elif 21 <= hour < 23:  # 亥时 21:00-22:59
            return 11
        else:
            return 0  # 默认返回子时
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setRenderHint(QPainter.SmoothPixmapTransform)
        
        # 绘制专业背景
        self.draw_professional_background(painter)
        
        # 绘制八卦系统
        self.draw_bagua_system(painter)
        
        # 绘制太极图
        self.draw_taiji(painter)
        
        # 绘制时钟
        self.draw_clock(painter)
        
        # 绘制时间文字
        self.draw_time_text(painter)
    
    def draw_professional_background(self, painter):
        """绘制专业背景"""
        center_x, center_y = self.current_size // 2, self.current_size // 2
        
        # 径向渐变背景
        gradient = QRadialGradient(center_x, center_y, self.current_size // 2)
        gradient.setColorAt(0, QColor(20, 10, 40))    # 深紫色中心
        gradient.setColorAt(0.3, QColor(10, 5, 20))   # 深蓝紫
        gradient.setColorAt(0.7, QColor(5, 2, 10))    # 深黑紫
        gradient.setColorAt(1, QColor(0, 0, 0))       # 纯黑边缘
        
        painter.fillRect(self.rect(), gradient)
        
        # 绘制星空效果
        for i in range(200):
            star_angle = (i * 137.5 + self.angle_offset * 0.1) % 360
            star_radius = (50 + (i % 3) * 100 + math.sin(self.angle_offset * 0.01 + i) * 50) * self.scale_factor
            star_x = center_x + math.cos(math.radians(star_angle)) * star_radius
            star_y = center_y + math.sin(math.radians(star_angle)) * star_radius
            
            if 0 <= star_x <= self.current_size and 0 <= star_y <= self.current_size:
                star_brightness = 100 + 100 * math.sin(self.angle_offset * 0.02 + i * 0.1)
                painter.setPen(QPen(QColor(255, 255, 255, int(star_brightness)), 1))
                painter.drawPoint(int(star_x), int(star_y))
    
    def draw_bagua_system(self, painter):
        """绘制12时辰表盘系统（高亮闪烁+八卦名称外置+五行+五脏）"""
        center_x, center_y = self.current_size // 2, self.current_size // 2
        radius = int(360 * self.scale_factor)  # 表盘半径（根据缩放调整）
        
        # 12时辰信息及五脏六腑 - 正确的时辰对应关系（子时在12点位置开始，顺时针排列）
        shichen_info = [
            ("子", "坎", 0, "胆", "23:00-1:00"),    # 12点位置
            ("丑", "艮", 30, "肝", "1:00-3:00"),     # 1点位置  
            ("寅", "震", 60, "肺", "3:00-5:00"),     # 2点位置
            ("卯", "巽", 90, "大肠", "5:00-7:00"),   # 3点位置
            ("辰", "离", 120, "胃", "7:00-9:00"),    # 4点位置
            ("巳", "坤", 150, "脾", "9:00-11:00"),   # 5点位置
            ("午", "兑", 180, "心", "11:00-13:00"),  # 6点位置
            ("未", "乾", 210, "小肠", "13:00-15:00"), # 7点位置
            ("申", "坎", 240, "膀胱", "15:00-17:00"), # 8点位置
            ("酉", "艮", 270, "肾", "17:00-19:00"),   # 9点位置
            ("戌", "震", 300, "心包", "19:00-21:00"), # 10点位置
            ("亥", "巽", 330, "三焦", "21:00-23:00")  # 11点位置
        ]
        # 八卦五行映射
        bagua_wuxing = {
            "乾": "金", "兑": "金", "离": "火", "震": "木", "巽": "木", "坎": "水", "艮": "土", "坤": "土"
        }
        # 当前时辰计算 - 修正为正确的时辰计算
        current_hour = self.current_time.hour
        current_shichen_index = self.get_current_shichen_index(current_hour)
        current_second = self.current_time.second
        flash_on = (current_second % 2 == 0)
        
        # 绘制12时辰标记
        for i, (shichen_name, bagua_name, angle, organ, time_range) in enumerate(shichen_info):
            clock_angle = angle - 90
            x = center_x + math.cos(math.radians(clock_angle)) * radius
            y = center_y + math.sin(math.radians(clock_angle)) * radius
            if i == current_shichen_index and flash_on:
                painter.setBrush(QBrush(QColor(255, 215, 0, 120)))
                painter.setPen(QPen(QColor(255, 255, 255), int(4 * self.scale_factor)))
                highlight_size = int(38 * self.scale_factor)
                painter.drawEllipse(int(x - highlight_size), int(y - highlight_size), highlight_size * 2, highlight_size * 2)
            painter.setFont(QFont("Microsoft YaHei", int(24 * self.scale_factor), QFont.Bold))  # 根据缩放调整字体
            painter.setPen(QPen(QColor(0, 0, 0, 150), int(2 * self.scale_factor)))
            text_rect = painter.fontMetrics().boundingRect(shichen_name)
            shadow_offset = int(2 * self.scale_factor)
            painter.drawText(int(x - text_rect.width()/2 + shadow_offset), int(y + text_rect.height()/3 + shadow_offset), shichen_name)
            gradient_size = int(20 * self.scale_factor)
            gradient = QLinearGradient(x - gradient_size, y - gradient_size, x + gradient_size, y + gradient_size)
            gradient.setColorAt(0, QColor(255, 215, 0))
            gradient.setColorAt(1, QColor(255, 165, 0))
            painter.setPen(QPen(gradient, int(2 * self.scale_factor)))
            painter.drawText(int(x - text_rect.width()/2), int(y + text_rect.height()/3), shichen_name)
        painter.setPen(QPen(QColor(255, 215, 0, 100), int(2 * self.scale_factor)))
        for i in range(12):
            angle = i * 30 - 90
            line_offset = int(20 * self.scale_factor)
            outer_x = center_x + math.cos(math.radians(angle)) * (radius + line_offset)
            outer_y = center_y + math.sin(math.radians(angle)) * (radius + line_offset)
            inner_x = center_x + math.cos(math.radians(angle)) * (radius - line_offset)
            inner_y = center_y + math.sin(math.radians(angle)) * (radius - line_offset)
            painter.drawLine(int(outer_x), int(outer_y), int(inner_x), int(inner_y))
        # 绘制中间八卦环及八卦名称、五行
        bagua_radius = int(140 * self.scale_factor)  # 根据缩放调整八卦半径
        name_offset = int(70 * self.scale_factor)    # 调整八卦名称偏移
        wuxing_offset = int(105 * self.scale_factor) # 调整五行偏移
        for i, (name, symbol, base_angle) in enumerate(self.bagua_info):
            bagua_angle = base_angle - 90
            bagua_x = center_x + math.cos(math.radians(bagua_angle)) * bagua_radius
            bagua_y = center_y + math.sin(math.radians(bagua_angle)) * bagua_radius
            current_bagua_name = shichen_info[current_shichen_index][1]
            if name == current_bagua_name and flash_on:
                painter.setBrush(QBrush(QColor(255, 215, 0, 120)))
                painter.setPen(QPen(QColor(255, 255, 255), int(3 * self.scale_factor)))
                bagua_highlight_size = int(32 * self.scale_factor)
                painter.drawEllipse(int(bagua_x - bagua_highlight_size), int(bagua_y - bagua_highlight_size), 
                                  bagua_highlight_size * 2, bagua_highlight_size * 2)
            painter.setFont(QFont("SimSun", int(32 * self.scale_factor), QFont.Bold))  # 根据缩放调整八卦符号字体
            painter.setPen(QPen(QColor(0, 0, 0, 150), int(3 * self.scale_factor)))
            symbol_rect = painter.fontMetrics().boundingRect(symbol)
            symbol_shadow_offset = int(2 * self.scale_factor)
            painter.drawText(int(bagua_x - symbol_rect.width()/2 + symbol_shadow_offset), 
                           int(bagua_y + symbol_rect.height()/3 + symbol_shadow_offset), symbol)
            symbol_gradient_size = int(25 * self.scale_factor)
            gradient = QLinearGradient(bagua_x - symbol_gradient_size, bagua_y - symbol_gradient_size, 
                                     bagua_x + symbol_gradient_size, bagua_y + symbol_gradient_size)
            gradient.setColorAt(0, QColor(255, 215, 0))
            gradient.setColorAt(1, QColor(255, 165, 0))
            painter.setPen(QPen(gradient, int(3 * self.scale_factor)))
            painter.drawText(int(bagua_x - symbol_rect.width()/2), int(bagua_y + symbol_rect.height()/3), symbol)
            # 八卦名称外置
            name_angle = math.radians(bagua_angle)
            name_x = center_x + math.cos(name_angle) * (bagua_radius + name_offset)
            name_y = center_y + math.sin(name_angle) * (bagua_radius + name_offset)
            painter.setFont(QFont("Microsoft YaHei", int(20 * self.scale_factor), QFont.Bold))
            painter.setPen(QPen(QColor(0, 0, 0, 120), int(1 * self.scale_factor)))
            name_rect = painter.fontMetrics().boundingRect(name)
            name_shadow_offset = int(1 * self.scale_factor)
            painter.drawText(int(name_x - name_rect.width()/2 + name_shadow_offset), 
                           int(name_y + name_rect.height()/3 + name_shadow_offset), name)
            painter.setPen(QPen(QColor(255, 255, 255), int(2 * self.scale_factor)))
            painter.drawText(int(name_x - name_rect.width()/2), int(name_y + name_rect.height()/3), name)
            # 五行外置（增加距离避免重叠）
            wuxing = bagua_wuxing.get(name, "")
            wuxing_x = center_x + math.cos(name_angle) * (bagua_radius + wuxing_offset)
            wuxing_y = center_y + math.sin(name_angle) * (bagua_radius + wuxing_offset)
            painter.setFont(QFont("Microsoft YaHei", int(18 * self.scale_factor), QFont.Bold))
            painter.setPen(QPen(QColor(255, 215, 0), int(2 * self.scale_factor)))
            wuxing_rect = painter.fontMetrics().boundingRect(wuxing)
            painter.drawText(int(wuxing_x - wuxing_rect.width()/2), int(wuxing_y + wuxing_rect.height()/3), wuxing)
        # 绘制所有五脏六腑在对应时辰位置
        base_organ_radius = int(310 * self.scale_factor)  # 基础五脏显示半径
        
        for i, (shichen_name, bagua_name, angle, organ, time_range) in enumerate(shichen_info):
            # 确保每次都设置统一的字体
            painter.setFont(QFont("Microsoft YaHei", int(18 * self.scale_factor), QFont.Bold))  # 统一字体大小18px
            
            # 根据具体器官调整距离
            organ_radius = base_organ_radius
            if organ == "膀胱":  # 申时 - 膀胱拉开1.5mm (约6像素)
                organ_radius = base_organ_radius - int(6 * self.scale_factor)
            elif organ == "心包":  # 戌时 - 心包拉开1mm (约4像素)
                organ_radius = base_organ_radius - int(4 * self.scale_factor)
            elif organ == "大肠":  # 卯时 - 大肠拉开0.5mm (约2像素)
                organ_radius = base_organ_radius - int(2 * self.scale_factor)
            
            # 计算器官位置（在时辰内侧）
            organ_angle = angle - 90  # 调整角度
            organ_x = center_x + math.cos(math.radians(organ_angle)) * organ_radius
            organ_y = center_y + math.sin(math.radians(organ_angle)) * organ_radius
            
            # 获取文字尺寸（确保使用统一字体）
            organ_rect = painter.fontMetrics().boundingRect(organ)
            
            # 如果是当前时辰，高亮显示
            if i == current_shichen_index and flash_on:
                # 绘制高亮背景圆圈
                painter.setBrush(QBrush(QColor(255, 215, 0, 80)))
                painter.setPen(QPen(QColor(255, 255, 255), int(2 * self.scale_factor)))
                highlight_padding_x = int(10 * self.scale_factor)
                highlight_padding_y = int(5 * self.scale_factor)
                painter.drawEllipse(int(organ_x - organ_rect.width()/2 - highlight_padding_x), 
                                  int(organ_y - organ_rect.height()/2 - highlight_padding_y), 
                                  organ_rect.width() + highlight_padding_x * 2, 
                                  organ_rect.height() + highlight_padding_y * 2)
            
            # 绘制器官名称阴影
            painter.setPen(QPen(QColor(0, 0, 0, 150), int(2 * self.scale_factor)))
            organ_shadow_offset = int(1 * self.scale_factor)
            painter.drawText(int(organ_x - organ_rect.width()/2 + organ_shadow_offset), 
                           int(organ_y + organ_rect.height()/3 + organ_shadow_offset), organ)
            
            # 绘制器官名称主体
            if i == current_shichen_index:
                # 当前时辰用金色
                painter.setPen(QPen(QColor(255, 215, 0), int(3 * self.scale_factor)))
            else:
                # 其他时辰用白色
                painter.setPen(QPen(QColor(255, 255, 255), int(2 * self.scale_factor)))
            painter.drawText(int(organ_x - organ_rect.width()/2), int(organ_y + organ_rect.height()/3), organ)
    
    def draw_taiji(self, painter):
        """绘制标准太极图（缩小版）"""
        center_x, center_y = self.current_size // 2, self.current_size // 2
        taiji_radius = int(75 * self.scale_factor)  # 根据缩放调整太极图半径
        
        # 太极图旋转
        rotation_angle = self.angle_offset * 0.5
        
        # 绘制太极图的光晕效果
        for glow_layer in range(8):
            glow_radius = taiji_radius + int((20 + glow_layer * 10) * self.scale_factor)
            glow_alpha = max(0, 150 - glow_layer * 15)
            glow_width = max(1, int((6 - glow_layer // 2) * self.scale_factor))
            painter.setPen(QPen(QColor(255, 215, 0, glow_alpha), glow_width))
            painter.setBrush(QBrush())
            painter.drawEllipse(center_x - glow_radius, center_y - glow_radius, 
                              glow_radius * 2, glow_radius * 2)
        
        # 保存绘图状态
        painter.save()
        
        # 移动到中心点并旋转
        painter.translate(center_x, center_y)
        painter.rotate(rotation_angle)
        
        # 绘制太极图外圈金边
        painter.setPen(QPen(QColor(255, 215, 0), int(6 * self.scale_factor)))
        painter.setBrush(QBrush())
        painter.drawEllipse(-taiji_radius, -taiji_radius, taiji_radius * 2, taiji_radius * 2)
        
        # 绘制白色背景（无边框）
        painter.setPen(QPen(Qt.NoPen))
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawEllipse(-taiji_radius, -taiji_radius, taiji_radius * 2, taiji_radius * 2)
        
        # 绘制黑色半圆（标准太极图的左半部分）
        painter.setBrush(QBrush(QColor(0, 0, 0)))
        painter.drawPie(-taiji_radius, -taiji_radius, taiji_radius * 2, taiji_radius * 2, 
                       90 * 16, 180 * 16)
        
        # 绘制上方小圆（白色，在黑色区域）
        small_radius = taiji_radius // 2
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawEllipse(-small_radius, -taiji_radius, small_radius * 2, small_radius * 2)
        
        # 绘制下方小圆（黑色，在白色区域）
        painter.setBrush(QBrush(QColor(0, 0, 0)))
        painter.drawEllipse(-small_radius, 0, small_radius * 2, small_radius * 2)
        
        # 绘制上方小圆中的黑点
        dot_radius = taiji_radius // 6
        painter.setBrush(QBrush(QColor(0, 0, 0)))
        painter.drawEllipse(-dot_radius, -small_radius, dot_radius * 2, dot_radius * 2)
        
        # 绘制下方小圆中的白点
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawEllipse(-dot_radius, small_radius - dot_radius, dot_radius * 2, dot_radius * 2)
        
        # 恢复绘图状态
        painter.restore()
    
    def draw_clock(self, painter):
        """绘制专业时钟指针（金色光束+射线秒针）"""
        center_x, center_y = self.current_size // 2, self.current_size // 2
        radius = int(350 * self.scale_factor)
        hour = self.current_time.hour % 12
        minute = self.current_time.minute
        second = self.current_time.second
        # 时针（金色光束）
        hour_angle = (hour + minute / 60.0) * 30 - 90
        self.draw_gold_beam(painter, hour_angle, radius * 0.35, int(18 * self.scale_factor), QColor(255, 255, 180), QColor(255, 215, 0))
        # 分针（金色光束）
        minute_angle = minute * 6 - 90
        self.draw_gold_beam(painter, minute_angle, radius * 0.55, int(12 * self.scale_factor), QColor(255, 255, 220), QColor(255, 215, 0))
        # 秒针（金色射线，每秒射一次）
        if second % 1 == 0:
            second_angle = second * 6 - 90
            self.draw_gold_ray(painter, second_angle, radius * 0.75, int(8 * self.scale_factor), QColor(255, 255, 180), QColor(255, 215, 0))
        # 中心点
        painter.setBrush(QBrush(QColor(255, 215, 0)))
        painter.setPen(QPen(QColor(255, 165, 0), int(3 * self.scale_factor)))
        center_size = int(15 * self.scale_factor)
        painter.drawEllipse(center_x - center_size, center_y - center_size, center_size * 2, center_size * 2)

    def draw_gold_beam(self, painter, angle, length, width, color1, color2):
        """绘制金色光束（时针/分针）"""
        center_x, center_y = self.current_size // 2, self.current_size // 2
        # 渐变光束
        gradient = QLinearGradient(center_x, center_y, 
                                 center_x + math.cos(math.radians(angle)) * length,
                                 center_y + math.sin(math.radians(angle)) * length)
        gradient.setColorAt(0, color1)
        gradient.setColorAt(0.5, color2)
        gradient.setColorAt(1, QColor(255, 255, 255, 0))
        painter.setPen(QPen(gradient, width, QtCore.Qt.SolidLine, QtCore.Qt.RoundCap))
        end_x = center_x + math.cos(math.radians(angle)) * length
        end_y = center_y + math.sin(math.radians(angle)) * length
        painter.drawLine(center_x, center_y, int(end_x), int(end_y))

    def draw_gold_ray(self, painter, angle, length, width, color1, color2):
        """绘制金色射线（秒针）"""
        center_x, center_y = self.current_size // 2, self.current_size // 2
        # 射线渐变
        gradient = QLinearGradient(center_x, center_y, 
                                 center_x + math.cos(math.radians(angle)) * length,
                                 center_y + math.sin(math.radians(angle)) * length)
        gradient.setColorAt(0, color1)
        gradient.setColorAt(0.7, color2)
        gradient.setColorAt(1, QColor(255, 255, 255, 0))
        painter.setPen(QPen(gradient, width, QtCore.Qt.SolidLine, QtCore.Qt.RoundCap))
        end_x = center_x + math.cos(math.radians(angle)) * length
        end_y = center_y + math.sin(math.radians(angle)) * length
        painter.drawLine(center_x, center_y, int(end_x), int(end_y))
    
    def draw_time_text(self, painter):
        """左上角显示阳历、阴历、时间三排（无外框黄色字体）"""
        # 获取阳历时间
        solar_date = self.current_time.strftime("%Y年%m月%d日")
        solar_time = self.current_time.strftime("%H:%M:%S")
        
        # 获取阴历时间
        lunar_date = ""
        try:
            if Lunar:
                lunar = Lunar.fromDate(self.current_time)
                lunar_date = f"{lunar.getYearInGanZhi()}年{lunar.getMonthInChinese()}月{lunar.getDayInChinese()}"
        except:
            lunar_date = "获取失败"
        

        
        # 左上角布局，与时钟边框平行
        margin = 10  # 左边距
        top_margin = 60  # 上边距，向上移动5mm（约20像素）
        
        # 使用设置中的字体大小，根据缩放调整
        time_font_size = self.settings_cache.get('time_font_size', 10)
        painter.setFont(QFont("Microsoft YaHei", int(time_font_size * self.scale_factor), QFont.Normal))
        
        # 获取字体度量信息
        font_metrics = painter.fontMetrics()
        line_height = font_metrics.height() + 6  # 增加行间距
        
        # 计算文字的起始Y位置
        text_start_y = top_margin + font_metrics.ascent()
        
        # 绘制四行文字（左对齐，黄色字体，无外框）
        painter.setPen(QPen(QColor(255, 215, 0), 2))  # 黄色字体
        
        # 第一行：阳历日期
        painter.drawText(margin, text_start_y, solar_date)
        
        # 第二行：阴历日期
        painter.drawText(margin, text_start_y + line_height, lunar_date)
        
        # 第三行：时间
        painter.drawText(margin, text_start_y + line_height * 2, solar_time)
        

        
        # 左下角显示版权信息
        self.draw_copyright_info(painter)
    
    def draw_copyright_info(self, painter):
        """在左下角显示版权信息"""
        # 版权信息文字
        copyright_text1 = "版权所有 © 2025 湖南全航信息通信有限公司"
        copyright_text2 = "保留所有权利。"
        
        # 设置字体（使用设置中的字体大小），根据缩放调整  
        time_font_size = self.settings_cache.get('time_font_size', 10)
        painter.setFont(QFont("Microsoft YaHei", int(time_font_size * self.scale_factor), QFont.Normal))
        
        # 获取字体度量信息
        font_metrics = painter.fontMetrics()
        line_height = font_metrics.height() + 2
        
        # 计算左下角位置
        margin = 10  # 左边距
        bottom_margin = 20  # 底边距
        
        # 计算Y位置（从底部向上）
        y_pos1 = self.height() - bottom_margin - line_height
        y_pos2 = self.height() - bottom_margin
        
        # 设置版权信息颜色（稍微透明的金色）
        painter.setPen(QPen(QColor(255, 215, 0, 180), 1))
        
        # 绘制版权信息
        painter.drawText(margin, y_pos1, copyright_text1)
        painter.drawText(margin, y_pos2, copyright_text2)
    

    
    def get_clicked_shichen(self, pos):
        """检测鼠标点击的时辰（支持缩放）"""
        # 根据缩放计算实际的中心点和半径
        center_x, center_y = self.current_size // 2, self.current_size // 2
        radius = int(360 * self.scale_factor)  # 时辰显示半径（根据缩放调整）
        
        # 计算点击位置相对于中心的距离和角度
        dx = pos.x() - center_x
        dy = pos.y() - center_y
        distance = math.sqrt(dx * dx + dy * dy)
        
        # 检查是否在时辰环形区域内（半径±40像素范围，也要缩放）
        tolerance = int(40 * self.scale_factor)
        if not (radius - tolerance <= distance <= radius + tolerance):
            return None
        
        # 计算角度（转换为0-360度）
        angle = math.degrees(math.atan2(dy, dx))
        if angle < 0:
            angle += 360
        
        # 调整角度，因为12点方向是0度（子时位置）
        # 从12点开始顺时针计算
        adjusted_angle = (angle + 90) % 360
        
        # 计算对应的时辰索引（每个时辰占30度）
        shichen_index = int(adjusted_angle / 30) % 12
        
        return shichen_index
    
    def show_shichen_info(self, shichen_index):
        """显示时辰详细信息"""
        # 12时辰详细信息
        shichen_details = [
            {
                "name": "子时",
                "time": "23:00-1:00",
                "bagua": "坎卦",
                "organ": "胆经",
                "function": "胆气生发，启动全身气血循环",
                "health": "务必睡眠，避免熬夜",
                "tips": "此时应深睡，让胆经充分休息，为明日生发阳气做准备。"
            },
            {
                "name": "丑时",
                "time": "1:00-3:00",
                "bagua": "艮卦",
                "organ": "肝经",
                "function": "肝藏血解毒，净化血液",
                "health": "深度睡眠，忌饮酒、用眼",
                "tips": "肝经当令，血归于肝。熬夜最伤肝，此时必须熟睡。"
            },
            {
                "name": "寅时",
                "time": "3:00-5:00",
                "bagua": "震卦",
                "organ": "肺经",
                "function": "肺分配气血至全身",
                "health": "保持熟睡，避免早晨或运动",
                "tips": "肺朝百脉，将气血输送全身。老人常在此时醒来属正常。"
            },
            {
                "name": "卯时",
                "time": "5:00-7:00",
                "bagua": "巽卦",
                "organ": "大肠经",
                "function": "排泄糟粕，清理肠道",
                "health": "喝温水+排便，按摩天枢穴促代谢",
                "tips": "大肠经当令，最佳排便时间。一杯温水促进肠道蠕动。"
            },
            {
                "name": "辰时",
                "time": "7:00-9:00",
                "bagua": "离卦",
                "organ": "胃经",
                "function": "胃腐熟食物，转化能量",
                "health": "吃营养早餐（如粥、鸡蛋），忌空腹",
                "tips": "胃经最旺，早餐要吃好。此时消化能力最强，营养吸收最佳。"
            },
            {
                "name": "巳时",
                "time": "9:00-11:00",
                "bagua": "坤卦",
                "organ": "脾经",
                "function": "脾运化营养，输布全身",
                "health": "高效工作/学习，适当活动四肢",
                "tips": "脾主运化，此时工作学习效率最高，大脑思维最活跃。"
            },
            {
                "name": "午时",
                "time": "11:00-13:00",
                "bagua": "兑卦",
                "organ": "心经",
                "function": "心主血脉，维持神志",
                "health": "午休20-30分钟，养心安神",
                "tips": "心经当令，适合小憩。午睡可养心神，下午精力更充沛。"
            },
            {
                "name": "未时",
                "time": "13:00-15:00",
                "bagua": "乾卦",
                "organ": "小肠经",
                "function": "小肠吸收营养，分清浊液",
                "health": "饭后补水（如绿茶），促营养吸收",
                "tips": "小肠分清浊，此时多喝水有助营养吸收和废物排泄。"
            },
            {
                "name": "申时",
                "time": "15:00-17:00",
                "bagua": "坎卦",
                "organ": "膀胱经",
                "function": "膀胱排泄水液，疏通阳气",
                "health": "多喝水+轻运动（如散步、敲打背部）",
                "tips": "膀胱经最长，此时适合运动排毒，多喝水促进代谢。"
            },
            {
                "name": "酉时",
                "time": "17:00-19:00",
                "bagua": "艮卦",
                "organ": "肾经",
                "function": "肾藏精华，调节生殖生长",
                "health": "清淡晚餐，按摩太溪穴护肾",
                "tips": "肾为先天之本，此时宜静养，晚餐清淡，保护肾精。"
            },
            {
                "name": "戌时",
                "time": "19:00-21:00",
                "bagua": "震卦",
                "organ": "心包经",
                "function": "心包代心行令，保护心脏",
                "health": "放松情绪（聊天、冥想），按摩内关穴",
                "tips": "心包护心主，宜放松心情，与家人交流，准备入睡。"
            },
            {
                "name": "亥时",
                "time": "21:00-23:00",
                "bagua": "巽卦",
                "organ": "三焦经",
                "function": "三焦通百脉，协调水气运行",
                "health": "准备睡眠，泡脚、冥想等活动",
                "tips": "三焦主气化，此时应准备睡眠，泡脚可助眠安神。"
            }
        ]
        
        if 0 <= shichen_index < len(shichen_details):
            info = shichen_details[shichen_index]
            self.show_shichen_dialog(info)
    
    def show_shichen_dialog(self, info):
        """显示时辰信息对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"{info['name']} - 详细信息")
        dialog.setFixedSize(500, 400)
        dialog.setModal(True)
        
        # 设置对话框样式
        dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
                border: 2px solid #FFD700;
                border-radius: 10px;
            }
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                padding: 5px;
                background: transparent;
            }
            QLabel#title {
                color: #FFD700;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
            }
            QLabel#subtitle {
                color: #FFA500;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 15px;
                color: #FFD700;
                font-weight: bold;
                padding: 10px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(f"{info['name']} ({info['time']})")
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 对应经络
        bagua_label = QLabel(f"对应经络：{info['bagua']} - {info['organ']}")
        bagua_label.setObjectName("subtitle")
        layout.addWidget(bagua_label)
        
        # 功能描述
        function_label = QLabel(f"功能作用：{info['function']}")
        layout.addWidget(function_label)
        
        # 养生要点
        health_label = QLabel(f"养生要点：{info['health']}")
        layout.addWidget(health_label)
        
        # 详细说明
        tips_label = QLabel(f"详细说明：{info['tips']}")
        tips_label.setWordWrap(True)
        layout.addWidget(tips_label)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)
        
        dialog.setLayout(layout)
        dialog.exec_()
    
    def handle_hover_change(self, hover_shichen):
        """处理鼠标悬停变化"""
        # 关闭当前悬停对话框
        if self.hover_dialog:
            self.hover_dialog.close()
            self.hover_dialog = None
        
        self.current_hover_shichen = hover_shichen
        
        # 如果鼠标悬停在时辰上，显示悬停对话框
        if hover_shichen is not None:
            self.show_hover_dialog(hover_shichen)
    
    def show_hover_dialog(self, shichen_index):
        """显示悬停对话框（单一框体，不透明背景）"""
        # 获取时辰详细信息
        shichen_details = [
            {
                "name": "子时",
                "time": "23:00-1:00",
                "bagua": "坎卦",
                "organ": "胆经",
                "function": "胆气生发，启动全身气血循环",
                "health": "务必睡眠，避免熬夜",
                "tips": "此时应深睡，让胆经充分休息，为明日生发阳气做准备。"
            },
            {
                "name": "丑时",
                "time": "1:00-3:00",
                "bagua": "艮卦",
                "organ": "肝经",
                "function": "肝藏血解毒，净化血液",
                "health": "深度睡眠，忌饮酒、用眼",
                "tips": "肝经当令，血归于肝。熬夜最伤肝，此时必须熟睡。"
            },
            {
                "name": "寅时",
                "time": "3:00-5:00",
                "bagua": "震卦",
                "organ": "肺经",
                "function": "肺分配气血至全身",
                "health": "保持熟睡，避免早晨或运动",
                "tips": "肺朝百脉，将气血输送全身。老人常在此时醒来属正常。"
            },
            {
                "name": "卯时",
                "time": "5:00-7:00",
                "bagua": "巽卦",
                "organ": "大肠经",
                "function": "排泄糟粕，清理肠道",
                "health": "喝温水+排便，按摩天枢穴促代谢",
                "tips": "大肠经当令，最佳排便时间。一杯温水促进肠道蠕动。"
            },
            {
                "name": "辰时",
                "time": "7:00-9:00",
                "bagua": "离卦",
                "organ": "胃经",
                "function": "胃腐熟食物，转化能量",
                "health": "吃营养早餐（如粥、鸡蛋），忌空腹",
                "tips": "胃经最旺，早餐要吃好。此时消化能力最强，营养吸收最佳。"
            },
            {
                "name": "巳时",
                "time": "9:00-11:00",
                "bagua": "坤卦",
                "organ": "脾经",
                "function": "脾运化营养，输布全身",
                "health": "高效工作/学习，适当活动四肢",
                "tips": "脾主运化，此时工作学习效率最高，大脑思维最活跃。"
            },
            {
                "name": "午时",
                "time": "11:00-13:00",
                "bagua": "兑卦",
                "organ": "心经",
                "function": "心主血脉，维持神志",
                "health": "午休20-30分钟，养心安神",
                "tips": "心经当令，适合小憩。午睡可养心神，下午精力更充沛。"
            },
            {
                "name": "未时",
                "time": "13:00-15:00",
                "bagua": "乾卦",
                "organ": "小肠经",
                "function": "小肠吸收营养，分清浊液",
                "health": "饭后补水（如绿茶），促营养吸收",
                "tips": "小肠分清浊，此时多喝水有助营养吸收和废物排泄。"
            },
            {
                "name": "申时",
                "time": "15:00-17:00",
                "bagua": "坎卦",
                "organ": "膀胱经",
                "function": "膀胱排泄水液，疏通阳气",
                "health": "多喝水+轻运动（如散步、敲打背部）",
                "tips": "膀胱经最长，此时适合运动排毒，多喝水促进代谢。"
            },
            {
                "name": "酉时",
                "time": "17:00-19:00",
                "bagua": "艮卦",
                "organ": "肾经",
                "function": "肾藏精华，调节生殖生长",
                "health": "清淡晚餐，按摩太溪穴护肾",
                "tips": "肾为先天之本，此时宜静养，晚餐清淡，保护肾精。"
            },
            {
                "name": "戌时",
                "time": "19:00-21:00",
                "bagua": "震卦",
                "organ": "心包经",
                "function": "心包代心行令，保护心脏",
                "health": "放松情绪（聊天、冥想），按摩内关穴",
                "tips": "心包护心主，宜放松心情，与家人交流，准备入睡。"
            },
            {
                "name": "亥时",
                "time": "21:00-23:00",
                "bagua": "巽卦",
                "organ": "三焦经",
                "function": "三焦通百脉，协调水气运行",
                "health": "准备睡眠，泡脚、冥想等活动",
                "tips": "三焦主气化，此时应准备睡眠，泡脚可助眠安神。"
            }
        ]
        
        if 0 <= shichen_index < len(shichen_details):
            info = shichen_details[shichen_index]
            
            # 创建悬停对话框（单一框体）
            self.hover_dialog = QWidget()
            self.hover_dialog.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
            self.hover_dialog.setFixedSize(380, 200)
            
            # 设置对话框样式（不透明背景，清晰可读）
            self.hover_dialog.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2A2A2A, stop:1 #1A1A1A);
                    border: 2px solid #FFD700;
                    border-radius: 10px;
                }
            """)
            
            # 创建单一标签显示所有信息
            info_text = f"""<div style="color: #FFD700; font-size: 16px; font-weight: bold; text-align: center; margin-bottom: 8px;">
{info['name']} ({info['time']})
</div>
<div style="color: #FFA500; font-size: 14px; font-weight: bold; margin-bottom: 6px;">
对应经络：{info['bagua']} - {info['organ']}
</div>
<div style="color: #FFFFFF; font-size: 12px; margin-bottom: 4px;">
功能作用：{info['function']}
</div>
<div style="color: #FFFFFF; font-size: 12px; margin-bottom: 4px;">
养生要点：{info['health']}
</div>
<div style="color: #FFFFFF; font-size: 12px;">
详细说明：{info['tips']}
</div>"""
            
            info_label = QLabel(info_text)
            info_label.setWordWrap(True)
            info_label.setAlignment(Qt.AlignTop)
            info_label.setStyleSheet("background: transparent; padding: 10px;")
            
            layout = QVBoxLayout()
            layout.setContentsMargins(0, 0, 0, 0)
            layout.addWidget(info_label)
            
            self.hover_dialog.setLayout(layout)
            
            # 计算对话框位置（鼠标旁边）
            cursor_pos = QCursor.pos()
            self.hover_dialog.move(cursor_pos.x() + 15, cursor_pos.y() + 15)
            
            # 显示对话框
            self.hover_dialog.show()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 检查是否点击了时辰区域
            clicked_shichen = self.get_clicked_shichen(event.pos())
            if clicked_shichen is not None:
                self.show_shichen_info(clicked_shichen)
                event.accept()
                return
            
            # 如果没有点击时辰，则进行窗口拖动
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPos() - self.drag_position)
            event.accept()
        else:
            # 检查鼠标悬停的时辰
            hover_shichen = self.get_clicked_shichen(event.pos())
            if hover_shichen != self.current_hover_shichen:
                # 鼠标移到新的时辰或离开时辰区域
                self.handle_hover_change(hover_shichen)
            event.accept()
    
    def wheelEvent(self, event):
        """鼠标滚轮事件 - 缩放时钟"""
        # 获取滚轮滚动方向
        delta = event.angleDelta().y()
        
        # 计算缩放步长（更精细的控制）
        zoom_step = 0.05
        
        if delta > 0:
            # 向上滚动 - 放大
            if self.scale_factor < 2.0:  # 最大放大到2倍
                self.scale_factor += zoom_step
                self.update_size()
        else:
            # 向下滚动 - 缩小
            if self.scale_factor > 0.5:  # 最小缩小到0.5倍
                self.scale_factor -= zoom_step
                self.update_size()
        
        event.accept()
    

    
    def load_alarms(self):
        """加载闹钟 - 增强异常处理"""
        try:
            import os
            if not os.path.exists('alarms.json'):
                print("📝 闹钟文件不存在，创建默认配置")
                self.alarms = []
                self.save_alarms()
                return
                
            with open('alarms.json', 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print("📝 闹钟文件为空，使用默认配置")
                    self.alarms = []
                    return
                    
                self.alarms = json.load(f)
                if not isinstance(self.alarms, list):
                    print("⚠️ 闹钟数据格式错误，重置为空列表")
                    self.alarms = []
                    
        except json.JSONDecodeError as e:
            print(f"⚠️ 闹钟文件JSON格式错误: {e}")
            self.alarms = []
            # 备份损坏的文件
            try:
                import shutil
                shutil.copy('alarms.json', 'alarms_backup.json')
                print("📝 已备份损坏的闹钟文件为 alarms_backup.json")
            except:
                pass
        except (IOError, OSError, PermissionError) as e:
            print(f"⚠️ 读取闹钟文件失败: {e}")
            self.alarms = []
        except Exception as e:
            print(f"⚠️ 加载闹钟时出现未知错误: {e}")
            self.alarms = []
    
    def save_alarms(self):
        """保存闹钟 - 增强异常处理"""
        try:
            # 先保存到临时文件，成功后再替换原文件
            temp_filename = 'alarms_temp.json'
            with open(temp_filename, 'w', encoding='utf-8') as f:
                json.dump(self.alarms, f, ensure_ascii=False, indent=2)
            
            # 验证写入的文件
            with open(temp_filename, 'r', encoding='utf-8') as f:
                json.load(f)  # 验证JSON格式正确
            
            # 替换原文件
            import shutil
            shutil.move(temp_filename, 'alarms.json')
            print("✅ 闹钟数据保存成功")
            
        except json.JSONEncodeError as e:
            print(f"⚠️ 闹钟数据JSON编码错误: {e}")
        except (IOError, OSError, PermissionError) as e:
            print(f"⚠️ 保存闹钟文件失败: {e}")
            # 清理临时文件
            try:
                import os
                if os.path.exists('alarms_temp.json'):
                    os.remove('alarms_temp.json')
            except:
                pass
        except Exception as e:
            print(f"⚠️ 保存闹钟时出现未知错误: {e}")
    
    def check_alarms(self):
        """检查闹钟 - 增强数据结构访问保护"""
        try:
            if not hasattr(self, 'alarms') or not isinstance(self.alarms, list):
                print("⚠️ 闹钟数据无效")
                return
                
            current_time = self.current_time.strftime("%H:%M")
            
            # 遍历闹钟列表，增加边界检查
            for i, alarm in enumerate(self.alarms):
                try:
                    # 检查闹钟数据完整性
                    if not isinstance(alarm, dict):
                        print(f"⚠️ 闹钟{i}数据格式错误，跳过")
                        continue
                        
                    if 'time' not in alarm:
                        print(f"⚠️ 闹钟{i}缺少时间信息，跳过")
                        continue
                    
                    alarm_time = alarm['time']
                    if not isinstance(alarm_time, str):
                        print(f"⚠️ 闹钟{i}时间格式错误，跳过")
                        continue
                    
                    # 检查是否需要触发闹钟
                    if alarm_time == current_time and not alarm.get('triggered', False):
                        self.trigger_alarm(alarm)
                        alarm['triggered'] = True
                        print(f"✅ 触发闹钟{i}: {alarm_time}")
                    elif alarm_time != current_time:
                        alarm['triggered'] = False
                        
                except Exception as e:
                    print(f"⚠️ 处理闹钟{i}时出错: {e}")
                    continue
                    
        except Exception as e:
            print(f"⚠️ 检查闹钟时出错: {e}")
    
    def trigger_alarm(self, alarm):
        """触发闹钟 - 增强音频播放异常处理"""
        music_played = False
        
        try:
            # 检查alarm参数有效性
            if not isinstance(alarm, dict):
                print("⚠️ 闹钟数据格式错误")
                alarm = {'note': '闹钟提醒'}
            
            # 获取音乐文件路径
            music_path = self.settings_cache.get('default_music_path', 'C:/Users/<USER>/Desktop/金光咒.mp3')
            
            # 检查文件是否存在
            import os
            if os.path.exists(music_path):
                try:
                    pygame.mixer.music.load(music_path)
                    pygame.mixer.music.play()
                    music_played = True
                    print(f"✅ 闹钟音乐播放成功: {music_path}")
                except pygame.error as e:
                    print(f"⚠️ pygame音频播放失败: {e}")
                except (OSError, IOError) as e:
                    print(f"⚠️ 音频文件访问失败: {e}")
            else:
                print(f"⚠️ 音乐文件不存在: {music_path}")
                
        except Exception as e:
            print(f"⚠️ 播放闹钟音乐时出错: {e}")
        
        # 显示闹钟消息
        try:
            alarm_note = alarm.get('note', '闹钟提醒') if isinstance(alarm, dict) else '闹钟提醒'
            QMessageBox.information(self, "闹钟", f"时间到了！\n{alarm_note}")
        except Exception as e:
            print(f"⚠️ 显示闹钟消息失败: {e}")
        
        # 停止音乐
        if music_played:
            try:
                pygame.mixer.music.stop()
                print("✅ 闹钟音乐已停止")
            except Exception as e:
                print(f"⚠️ 停止闹钟音乐时出错: {e}")

    def check_shichen_reminder(self):
        """检查时辰自动提醒"""
        # 检查时辰提醒是否启用
        if not self.settings_cache.get('shichen_reminder_enabled', True):
            return
            
        current_hour = self.current_time.hour
        current_minute = self.current_time.minute
        
        # 计算当前时辰索引
        current_shichen_index = (current_hour + 1) // 2 % 12
        
        # 时辰切换时间点（每个时辰的开始时间）
        shichen_start_times = [
            (23, 0),  # 子时 23:00
            (1, 0),   # 丑时 1:00
            (3, 0),   # 寅时 3:00
            (5, 0),   # 卯时 5:00
            (7, 0),   # 辰时 7:00
            (9, 0),   # 巳时 9:00
            (11, 0),  # 午时 11:00
            (13, 0),  # 未时 13:00
            (15, 0),  # 申时 15:00
            (17, 0),  # 酉时 17:00
            (19, 0),  # 戌时 19:00
            (21, 0)   # 亥时 21:00
        ]
        
        # 检查是否到达时辰切换时间（前5分钟内）
        for i, (start_hour, start_minute) in enumerate(shichen_start_times):
            if current_hour == start_hour and 0 <= current_minute <= 5:
                # 检查是否已经显示过这个时辰的提醒
                reminder_key = f"{i}_{self.current_time.strftime('%Y%m%d')}"
                if reminder_key not in self.shichen_reminder_shown:
                    self.show_shichen_info(i)
                    self.shichen_reminder_shown[reminder_key] = True
                    break
        
        # 清理过期的提醒状态（非当天的记录）
        if hasattr(self, 'shichen_reminder_shown'):
            for key in list(self.shichen_reminder_shown.keys()):
                if not key.endswith(self.current_time.strftime('%Y%m%d')):
                    del self.shichen_reminder_shown[key]

    def check_health_reminder(self):
        """检查健康提醒"""
        # 检查健康提醒是否启用
        if not self.settings_cache.get('health_reminder_enabled', True):
            return
            
        current_hour = self.current_time.hour
        current_minute = self.current_time.minute
        
        # 健康提醒时间点
        health_reminders = {
            6: "🌅 早安！建议起床后喝一杯温水",
            8: "🍳 早餐时间，记得营养搭配",
            10: "💧 工作间隙，记得喝水",
            12: "🍽️ 午餐时间，建议适量运动",
            14: "☕ 下午茶时间，可以喝杯茶",
            16: "🚶 适合散步，活动筋骨",
            18: "🍽️ 晚餐时间，建议清淡饮食",
            20: "📚 学习时间，注意用眼卫生",
            22: "😴 准备睡觉，建议泡脚",
            23: "🌙 晚安时间，早点休息"
        }
        
        # 检查是否需要提醒（只在整点显示一次）
        if current_hour in health_reminders and current_minute == 0:
            # 检查是否已经显示过这个小时的提醒
            reminder_key = f"{current_hour}_{self.current_time.strftime('%Y%m%d')}"
            if not hasattr(self, 'health_reminder_shown'):
                self.health_reminder_shown = {}
            
            if reminder_key not in self.health_reminder_shown:
                self.show_health_reminder(health_reminders[current_hour])
                self.health_reminder_shown[reminder_key] = True
        elif current_minute != 0:
            # 清除非整点的提醒状态
            if hasattr(self, 'health_reminder_shown'):
                for key in list(self.health_reminder_shown.keys()):
                    if not key.endswith(self.current_time.strftime('%Y%m%d')):
                        del self.health_reminder_shown[key]

    def show_health_reminder(self, message):
        """显示健康提醒弹窗"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("健康提醒")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint | Qt.WindowCloseButtonHint)
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
                border: 2px solid #FFD700;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 15px;
                color: #FFD700;
                font-weight: bold;
                padding: 10px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        
        # 播放音乐 - 增强异常处理
        music_played = False
        try:
            # 从缓存设置中读取音乐路径和音量
            music_path = self.settings_cache.get('default_music_path', 'C:/Users/<USER>/Desktop/金光咒.mp3')
            volume = self.settings_cache.get('volume', 70) / 100.0
            
            # 检查文件是否存在
            import os
            if os.path.exists(music_path):
                try:
                    pygame.mixer.music.load(music_path)
                    pygame.mixer.music.set_volume(max(0.0, min(1.0, volume)))  # 确保音量在有效范围
                    pygame.mixer.music.play()
                    music_played = True
                    print(f"✅ 健康提醒音乐播放成功")
                except pygame.error as e:
                    print(f"⚠️ pygame音频播放失败: {e}")
                except (OSError, IOError) as e:
                    print(f"⚠️ 音频文件访问失败: {e}")
            else:
                print(f"⚠️ 音乐文件不存在: {music_path}")
        except Exception as e:
            print(f"⚠️ 播放健康提醒音乐时出错: {e}")
        
        msg_box.exec_()
        
        # 停止音乐
        if music_played:
            try:
                pygame.mixer.music.stop()
                print("✅ 健康提醒音乐已停止")
            except Exception as e:
                print(f"⚠️ 停止健康提醒音乐时出错: {e}")
    
    def load_system_settings(self):
        """加载系统设置 - 增强异常处理"""
        default_settings = {
            'default_music_path': 'C:/Users/<USER>/Desktop/金光咒.mp3',
            'notepad_enabled': True,
            'alarm_enabled': True,
            'health_reminder_enabled': True,
            'shichen_reminder_enabled': True,

            'lunar_display_enabled': True,
            'volume': 70,
            'time_font_size': 10,
            'window_opacity': 100,
            'music_duration': 15,
            'health_reminder_interval': 60,
            'shichen_advance_minutes': 5,
            'autostart_enabled': False,
            'default_scale': 60,
            'camera_enabled': True
        }
        
        try:
            import os
            if not os.path.exists('system_settings.json'):
                print("📝 系统设置文件不存在，创建默认配置")
                return default_settings
                
            with open('system_settings.json', 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print("📝 系统设置文件为空，使用默认配置")
                    return default_settings
                    
                settings = json.load(f)
                if not isinstance(settings, dict):
                    print("⚠️ 系统设置数据格式错误，使用默认配置")
                    return default_settings
                
                # 合并默认设置，确保所有必需的键都存在
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                        
                return settings
                
        except json.JSONDecodeError as e:
            print(f"⚠️ 系统设置文件JSON格式错误: {e}")
            # 备份损坏的文件
            try:
                import shutil
                shutil.copy('system_settings.json', 'system_settings_backup.json')
                print("📝 已备份损坏的设置文件为 system_settings_backup.json")
            except:
                pass
            return default_settings
        except (IOError, OSError, PermissionError) as e:
            print(f"⚠️ 读取系统设置文件失败: {e}")
            return default_settings
        except Exception as e:
            print(f"⚠️ 加载系统设置时出现未知错误: {e}")
            return default_settings
    
    def closeEvent(self, event):
        """主程序关闭事件 - 防止意外关闭，增强安全检查"""
        try:
            # 如果是通过确认对话框关闭的，直接接受
            if hasattr(self, '_confirmed_close') and self._confirmed_close:
                # 进行安全的资源清理
                self._safe_cleanup()
                event.accept()
                return
            
            # 否则询问用户是否真的要关闭
            try:
                msg = QMessageBox(self)
                msg.setWindowTitle('确认退出')
                msg.setText('确定要退出传统健康智慧时钟吗？\n\n主程序退出后：\n• 健康监测功能将停止\n• 时间提醒功能将停止\n• 数据会自动保存\n\n如果只是想关闭警告窗口，请点击"否"')
                msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
                msg.setDefaultButton(QMessageBox.No)
                msg.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint | Qt.WindowCloseButtonHint)
                reply = msg.exec_()
                
                if reply == QMessageBox.Yes:
                    # 标记为确认关闭
                    self._confirmed_close = True
                    
                    # 进行安全的资源清理
                    self._safe_cleanup()
                    
                    event.accept()
                else:
                    # 用户选择不关闭主程序
                    event.ignore()
                    
            except Exception as e:
                print(f"⚠️ 显示关闭确认对话框失败: {e}")
                # 如果对话框失败，允许直接关闭但进行清理
                self._safe_cleanup()
                event.accept()
                
        except Exception as e:
            print(f"⚠️ 处理关闭事件时出错: {e}")
            # 最后的安全措施
            event.accept()
    
    def _safe_cleanup(self):
        """安全的资源清理"""
        try:
            print("🔄 开始安全资源清理...")
            
            # 保存所有数据
            try:
                self.save_alarms()
                print("✅ 闹钟数据已保存")
            except Exception as e:
                print(f"⚠️ 保存闹钟数据失败: {e}")
            
            # 安全关闭所有子窗口
            window_list = [
                ('advanced_health_monitor_window', '高级健康监测'),
                ('ai_health_monitor_window', 'AI健康监测'),
                ('health_monitor_window', '基础健康监测'),
                ('notepad_window', '记事本'),
                ('alarm_window', '闹钟设置'),
                ('settings_window', '系统设置')
            ]
            
            for window_attr, window_name in window_list:
                if hasattr(self, window_attr) and getattr(self, window_attr):
                    try:
                        window_obj = getattr(self, window_attr)
                        
                        # 特殊处理健康监测窗口
                        if 'health_monitor' in window_attr:
                            # 检查并停止监测
                            if hasattr(window_obj, 'is_monitoring') and window_obj.is_monitoring:
                                if hasattr(window_obj, 'stop_monitoring'):
                                    window_obj.stop_monitoring()
                                    print(f"✅ {window_name}监测已停止")
                            
                            # 保存设置
                            if hasattr(window_obj, 'save_settings'):
                                window_obj.save_settings()
                                print(f"✅ {window_name}设置已保存")
                        
                        # 关闭窗口
                        if hasattr(window_obj, 'close'):
                            window_obj.close()
                            print(f"✅ {window_name}窗口已关闭")
                        
                        # 清空引用
                        setattr(self, window_attr, None)
                        
                    except Exception as e:
                        print(f"⚠️ 关闭{window_name}窗口失败: {e}")
            
            # 停止所有定时器
            timer_list = ['time_timer', 'animation_timer', 'reminder_timer']
            for timer_name in timer_list:
                try:
                    if hasattr(self, timer_name):
                        timer_obj = getattr(self, timer_name)
                        if timer_obj and hasattr(timer_obj, 'stop'):
                            timer_obj.stop()
                            print(f"✅ {timer_name}已停止")
                except Exception as e:
                    print(f"⚠️ 停止{timer_name}失败: {e}")
            
            # 停止音乐播放
            try:
                pygame.mixer.music.stop()
                print("✅ 音乐播放已停止")
            except:
                pass
            
            print("✅ 安全资源清理完成")
            
        except Exception as e:
            print(f"⚠️ 安全资源清理失败: {e}")


class SystemSettingsWindow(QWidget):
    """系统设置窗口"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setWindowTitle("系统设置 - 传统健康智慧时钟 V1.1.2")
        self.setFixedSize(700, 650)
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint | Qt.WindowCloseButtonHint)
        
        self.settings = self.load_settings()
        self.init_ui()
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
            }
            QGroupBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFD700;
                font-weight: bold;
                font-size: 12px;
                padding: 8px;
                margin-top: 8px;
                margin-bottom: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 12px;
                padding: 8px;
            }
            QCheckBox {
                color: #FFFFFF;
                font-size: 12px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #FFD700;
                border-radius: 3px;
                background: #2A2A2A;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #FFD700;
                border-radius: 3px;
                background: #FFD700;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0iIzAwMDAwMCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 12px;
                color: #FFD700;
                font-weight: bold;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
            QLabel {
                color: #FFD700;
                font-weight: bold;
                font-size: 12px;
            }
        """)
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 5, 10, 10)
        main_layout.setSpacing(8)
        
        # 标题
        title = QLabel("⚙️ 系统设置")
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
                background: transparent;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #2A2A2A;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #FFD700;
                border-radius: 6px;
                min-height: 20px;
            }
        """)
        
        # 滚动内容区域
        scroll_content = QWidget()
        content_layout = QVBoxLayout(scroll_content)
        content_layout.setSpacing(6)
        
        # 音乐设置组
        music_group = QGroupBox("🎵 音乐设置")
        music_layout = QVBoxLayout()
        music_layout.setSpacing(5)
        
        # 默认音乐路径
        music_path_layout = QHBoxLayout()
        music_path_layout.addWidget(QLabel("音乐文件:"))
        
        self.music_path_edit = QLineEdit(self.settings.get('default_music_path', 'C:/Users/<USER>/Desktop/金光咒.mp3'))
        self.music_path_edit.setMinimumHeight(25)
        music_path_layout.addWidget(self.music_path_edit)
        
        browse_btn = QPushButton("浏览")
        browse_btn.setFixedSize(50, 25)
        browse_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFD700;
                font-weight: bold;
                font-size: 11px;
                padding: 4px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        browse_btn.clicked.connect(self.browse_music_file)
        music_path_layout.addWidget(browse_btn)
        
        test_music_btn = QPushButton("试听")
        test_music_btn.setFixedSize(50, 25)
        test_music_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFD700;
                font-weight: bold;
                font-size: 11px;
                padding: 4px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        test_music_btn.clicked.connect(self.test_default_music)
        music_path_layout.addWidget(test_music_btn)
        
        music_layout.addLayout(music_path_layout)
        music_group.setLayout(music_layout)
        
        # 功能开关组
        function_group = QGroupBox("🔧 功能模块")
        function_layout = QGridLayout()
        function_layout.setSpacing(5)
        
        self.notepad_checkbox = QCheckBox("智能记事本")
        self.notepad_checkbox.setChecked(self.settings.get('notepad_enabled', True))
        
        self.alarm_checkbox = QCheckBox("闹钟提醒")
        self.alarm_checkbox.setChecked(self.settings.get('alarm_enabled', True))
        
        self.health_reminder_checkbox = QCheckBox("健康提醒")
        self.health_reminder_checkbox.setChecked(self.settings.get('health_reminder_enabled', True))
        
        self.shichen_reminder_checkbox = QCheckBox("时辰提醒")
        self.shichen_reminder_checkbox.setChecked(self.settings.get('shichen_reminder_enabled', True))
        

        
        self.lunar_display_checkbox = QCheckBox("农历显示")
        self.lunar_display_checkbox.setChecked(self.settings.get('lunar_display_enabled', True))
        
        # 健康监测摄像头开关
        if ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE:
            self.camera_enabled_checkbox = QCheckBox("🏥 摄像头监测")
            self.camera_enabled_checkbox.setChecked(self.settings.get('camera_enabled', True))
            self.camera_enabled_checkbox.setToolTip("启用/禁用摄像头健康监测功能")
        
        function_layout.addWidget(self.notepad_checkbox, 0, 0)
        function_layout.addWidget(self.alarm_checkbox, 0, 1)
        function_layout.addWidget(self.health_reminder_checkbox, 1, 0)
        function_layout.addWidget(self.shichen_reminder_checkbox, 1, 1)
        function_layout.addWidget(self.lunar_display_checkbox, 2, 0)
        
        if ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE:
            function_layout.addWidget(self.camera_enabled_checkbox, 3, 0)
        
        function_group.setLayout(function_layout)
        
        # 显示设置组
        display_group = QGroupBox("🎨 显示设置")
        display_layout = QVBoxLayout()
        display_layout.setSpacing(5)
        
        # 时间字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("时间字体:"))
        
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setRange(8, 16)
        self.font_size_slider.setValue(self.settings.get('time_font_size', 10))
        self.font_size_slider.valueChanged.connect(self.update_font_size_label)
        
        self.font_size_label = QLabel(f"{self.font_size_slider.value()}px")
        self.font_size_label.setFixedWidth(35)
        
        font_size_layout.addWidget(self.font_size_slider)
        font_size_layout.addWidget(self.font_size_label)
        
        # 透明度设置
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("窗口透明度:"))
        
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(50, 100)
        self.opacity_slider.setValue(self.settings.get('window_opacity', 100))
        self.opacity_slider.valueChanged.connect(self.update_opacity_label)
        
        self.opacity_label = QLabel(f"{self.opacity_slider.value()}%")
        self.opacity_label.setFixedWidth(35)
        
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        
        display_layout.addLayout(font_size_layout)
        display_layout.addLayout(opacity_layout)
        display_group.setLayout(display_layout)
        
        # 音频设置组
        audio_group = QGroupBox("🔊 音频设置")
        audio_layout = QVBoxLayout()
        audio_layout.setSpacing(5)
        
        # 提醒音量
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("提醒音量:"))
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(self.settings.get('volume', 70))
        self.volume_slider.valueChanged.connect(self.update_volume_label)
        
        self.volume_label = QLabel(f"{self.volume_slider.value()}%")
        self.volume_label.setFixedWidth(35)
        
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_label)
        
        # 音乐播放时长
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("播放时长:"))
        
        self.duration_spinbox = QSpinBox()
        self.duration_spinbox.setRange(5, 60)
        self.duration_spinbox.setValue(self.settings.get('music_duration', 15))
        self.duration_spinbox.setSuffix("秒")
        self.duration_spinbox.setMinimumHeight(30)
        self.duration_spinbox.setStyleSheet("""
            QSpinBox {
                background: #5A5A5A;
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                padding: 6px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border: 1px solid #FFD700;
                width: 14px;
                height: 10px;
                border-radius: 2px;
            }
            QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                margin-right: 1px;
                margin-top: 1px;
            }
            QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                margin-right: 1px;
                margin-bottom: 1px;
            }
        """)
        
        duration_layout.addWidget(self.duration_spinbox)
        duration_layout.addStretch()
        
        audio_layout.addLayout(volume_layout)
        audio_layout.addLayout(duration_layout)
        audio_group.setLayout(audio_layout)
        
        # 提醒设置组
        reminder_group = QGroupBox("⏰ 提醒设置")
        reminder_layout = QVBoxLayout()
        reminder_layout.setSpacing(5)
        
        # 健康提醒间隔
        health_interval_layout = QHBoxLayout()
        health_interval_layout.addWidget(QLabel("健康提醒间隔:"))
        
        self.health_interval_spinbox = QSpinBox()
        self.health_interval_spinbox.setRange(30, 240)
        self.health_interval_spinbox.setValue(self.settings.get('health_reminder_interval', 60))
        self.health_interval_spinbox.setSuffix("分钟")
        self.health_interval_spinbox.setMinimumHeight(30)
        self.health_interval_spinbox.setStyleSheet("""
            QSpinBox {
                background: #5A5A5A;
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                padding: 6px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border: 1px solid #FFD700;
                width: 14px;
                height: 10px;
                border-radius: 2px;
            }
            QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                margin-right: 1px;
                margin-top: 1px;
            }
            QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                margin-right: 1px;
                margin-bottom: 1px;
            }
        """)
        
        health_interval_layout.addWidget(self.health_interval_spinbox)
        health_interval_layout.addStretch()
        
        # 时辰提醒提前时间
        shichen_advance_layout = QHBoxLayout()
        shichen_advance_layout.addWidget(QLabel("时辰提醒提前:"))
        
        self.shichen_advance_spinbox = QSpinBox()
        self.shichen_advance_spinbox.setRange(0, 10)
        self.shichen_advance_spinbox.setValue(self.settings.get('shichen_advance_minutes', 5))
        self.shichen_advance_spinbox.setSuffix("分钟")
        self.shichen_advance_spinbox.setMinimumHeight(30)
        self.shichen_advance_spinbox.setStyleSheet("""
            QSpinBox {
                background: #5A5A5A;
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                padding: 6px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border: 1px solid #FFD700;
                width: 14px;
                height: 10px;
                border-radius: 2px;
            }
            QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                margin-right: 1px;
                margin-top: 1px;
            }
            QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                margin-right: 1px;
                margin-bottom: 1px;
            }
        """)
        
        shichen_advance_layout.addWidget(self.shichen_advance_spinbox)
        shichen_advance_layout.addStretch()
        
        reminder_layout.addLayout(health_interval_layout)
        reminder_layout.addLayout(shichen_advance_layout)
        reminder_group.setLayout(reminder_layout)
        
        # 系统设置组
        system_group = QGroupBox("🖥️ 系统设置")
        system_layout = QVBoxLayout()
        system_layout.setSpacing(5)
        
        # 开机自启动
        self.autostart_checkbox = QCheckBox("开机自动启动")
        self.autostart_checkbox.setChecked(self.settings.get('autostart_enabled', False))
        
        # 缩放设置
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("当前缩放:"))
        
        self.scale_slider = QSlider(Qt.Horizontal)
        self.scale_slider.setRange(50, 200)
        
        # 显示当前主窗口的实际缩放比例
        current_scale = int(self.settings.get('default_scale', 100))
        if self.parent_window:
            current_scale = int(self.parent_window.scale_factor * 100)
        
        self.scale_slider.setValue(current_scale)
        self.scale_slider.valueChanged.connect(self.update_scale_label)
        
        self.scale_label = QLabel(f"{self.scale_slider.value()}%")
        self.scale_label.setFixedWidth(35)
        
        scale_layout.addWidget(self.scale_slider)
        scale_layout.addWidget(self.scale_label)
        
        system_layout.addWidget(self.autostart_checkbox)
        system_layout.addLayout(scale_layout)
        system_group.setLayout(system_layout)
        
        # 添加所有组到内容布局
        content_layout.addWidget(music_group)
        content_layout.addWidget(function_group)
        content_layout.addWidget(display_group)
        content_layout.addWidget(audio_group)
        content_layout.addWidget(reminder_group)
        content_layout.addWidget(system_group)
        content_layout.addStretch()
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(12)
        
        save_btn = QPushButton("💾 保存设置")
        save_btn.setFixedSize(120, 40)
        save_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        
        reset_btn = QPushButton("🔄 恢复默认")
        reset_btn.setFixedSize(120, 40)
        reset_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
            }
        """)
        reset_btn.clicked.connect(self.reset_settings)
        
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setFixedSize(100, 40)
        cancel_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
            }
        """)
        cancel_btn.clicked.connect(self.close)
        
        button_layout.addWidget(save_btn)
        button_layout.addWidget(reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        
        # 添加到主布局
        main_layout.addWidget(title)
        main_layout.addWidget(scroll_area)
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
    
    def browse_music_file(self):
        """浏览音乐文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择音乐文件", 
            "C:/Users/<USER>/Desktop/", 
            "音乐文件 (*.mp3 *.wav *.ogg);;所有文件 (*)"
        )
        if file_path:
            self.music_path_edit.setText(file_path)
    
    def test_default_music(self):
        """测试默认音乐"""
        try:
            music_path = self.music_path_edit.text().strip()
            if not music_path:
                QMessageBox.warning(self, "警告", "请先设置音乐路径！")
                return
            
            pygame.mixer.music.load(music_path)
            pygame.mixer.music.set_volume(self.volume_slider.value() / 100.0)
            pygame.mixer.music.play()
            
            QMessageBox.information(self, "测试", "正在播放默认音乐...")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法播放音乐：{str(e)}")
    
    def update_volume_label(self, value):
        """更新音量标签"""
        self.volume_label.setText(f"{value}%")
    
    def update_font_size_label(self, value):
        """更新字体大小标签"""
        self.font_size_label.setText(f"{value}px")
    
    def update_opacity_label(self, value):
        """更新透明度标签"""
        self.opacity_label.setText(f"{value}%")
    
    def update_scale_label(self, value):
        """更新缩放标签"""
        self.scale_label.setText(f"{value}%")
    
    def load_settings(self):
        """加载设置"""
        try:
            with open('system_settings.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {
                'default_music_path': 'C:/Users/<USER>/Desktop/金光咒.mp3',
                'notepad_enabled': True,
                'alarm_enabled': True,
                'health_reminder_enabled': True,
                'shichen_reminder_enabled': True,

                'lunar_display_enabled': True,
                'volume': 70,
                'time_font_size': 10,
                'window_opacity': 100,
                'music_duration': 15,
                'health_reminder_interval': 60,
                'shichen_advance_minutes': 5,
                'autostart_enabled': False,
                'default_scale': 60,
                'camera_enabled': True
            }
    
    def save_settings(self):
        """保存设置"""
        try:
            # 获取当前主窗口的缩放比例
            current_scale = 100  # 默认值
            if self.parent_window:
                current_scale = int(self.parent_window.scale_factor * 100)
            
            settings = {
                'default_music_path': self.music_path_edit.text().strip(),
                'notepad_enabled': self.notepad_checkbox.isChecked(),
                'alarm_enabled': self.alarm_checkbox.isChecked(),
                'health_reminder_enabled': self.health_reminder_checkbox.isChecked(),
                'shichen_reminder_enabled': self.shichen_reminder_checkbox.isChecked(),

                'lunar_display_enabled': self.lunar_display_checkbox.isChecked(),
                'volume': self.volume_slider.value(),
                'time_font_size': self.font_size_slider.value(),
                'window_opacity': self.opacity_slider.value(),
                'music_duration': self.duration_spinbox.value(),
                'health_reminder_interval': self.health_interval_spinbox.value(),
                'shichen_advance_minutes': self.shichen_advance_spinbox.value(),
                'autostart_enabled': self.autostart_checkbox.isChecked(),
                'default_scale': current_scale  # 保存当前实际缩放比例
            }
            
            # 添加健康监测摄像头设置
            if ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE:
                settings['camera_enabled'] = self.camera_enabled_checkbox.isChecked()
            
            with open('system_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            # 设置开机自启动
            self.set_autostart(settings['autostart_enabled'])
            
            # 立即应用设置到主窗口
            self.apply_settings_immediately(settings)
            
            QMessageBox.information(self, "成功", "设置已保存并立即生效！")
            # 移除自动关闭，让用户手动关闭设置窗口
            # self.close()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存设置失败：{str(e)}")
    
    def apply_settings_immediately(self, settings):
        """立即应用设置到主窗口"""
        try:
            if self.parent_window:
                # 应用窗口透明度
                opacity = settings.get('window_opacity', 100) / 100.0
                self.parent_window.setWindowOpacity(opacity)
                
                # 保存当前缩放比例到设置中，而不是应用设置中的缩放比例
                current_scale_percent = int(self.parent_window.scale_factor * 100)
                settings['default_scale'] = current_scale_percent
                
                # 更新主窗口的系统设置缓存
                self.parent_window.settings_cache = settings
                
                # 应用音量设置到pygame
                try:
                    volume = settings.get('volume', 70) / 100.0
                    pygame.mixer.music.set_volume(volume)
                except:
                    pass
                
                # 重新初始化功能按钮状态（根据enabled设置）
                if hasattr(self.parent_window, 'notepad_btn'):
                    self.parent_window.notepad_btn.setEnabled(settings.get('notepad_enabled', True))
                if hasattr(self.parent_window, 'alarm_btn'):
                    self.parent_window.alarm_btn.setEnabled(settings.get('alarm_enabled', True))
                
                # 强制重绘主窗口以应用时间字体大小等设置
                self.parent_window.update()
                
                print(f"设置已立即应用：透明度={opacity*100}%, 缩放={current_scale_percent}%, 音量={settings.get('volume', 70)}%")
                
        except Exception as e:
            print(f"立即应用设置时出错：{str(e)}")
    
    def reset_settings(self):
        """恢复默认设置"""
        # 首先验证密码
        if not self.verify_reset_password():
            return
        
        # 重要警告对话框
        warning_dialog = QMessageBox(self)
        warning_dialog.setWindowTitle("⚠️ 重要警告")
        warning_dialog.setIcon(QMessageBox.Warning)
        warning_dialog.setText("恢复默认设置将会：")
        warning_dialog.setInformativeText(
            "1. 重置所有系统设置为默认值\n"
            "2. 清除记事本所有内容\n"
            "3. 清除记事本密码\n"
            "4. 删除所有日程和提醒\n"
            "5. 清空闹钟设置\n\n"
            "此操作不可恢复！确定要继续吗？"
        )
        warning_dialog.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
                border: 2px solid #FF6666;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 12px;
                color: #FFD700;
                font-weight: bold;
                padding: 8px 15px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        
        # 自定义按钮
        confirm_btn = warning_dialog.addButton("确认重置", QMessageBox.YesRole)
        cancel_btn = warning_dialog.addButton("取消", QMessageBox.NoRole)
        
        warning_dialog.exec_()
        
        if warning_dialog.clickedButton() == confirm_btn:
            try:
                # 重置界面设置
                self.music_path_edit.setText('C:/Users/<USER>/Desktop/金光咒.mp3')
                self.notepad_checkbox.setChecked(True)
                self.alarm_checkbox.setChecked(True)
                self.health_reminder_checkbox.setChecked(True)
                self.shichen_reminder_checkbox.setChecked(True)

                self.lunar_display_checkbox.setChecked(True)
                if ADVANCED_HEALTH_MONITOR_AVAILABLE or AI_HEALTH_MONITOR_AVAILABLE or HEALTH_MONITOR_AVAILABLE:
                    self.camera_enabled_checkbox.setChecked(True)
                self.volume_slider.setValue(70)
                self.font_size_slider.setValue(10)
                self.opacity_slider.setValue(100)
                self.duration_spinbox.setValue(15)
                self.health_interval_spinbox.setValue(60)
                self.shichen_advance_spinbox.setValue(5)
                self.autostart_checkbox.setChecked(False)
                self.scale_slider.setValue(60)
                
                # 清除所有数据文件
                self.clear_all_data_files()
                
                # 立即应用默认设置
                default_settings = {
                    'default_music_path': 'C:/Users/<USER>/Desktop/金光咒.mp3',
                    'notepad_enabled': True,
                    'alarm_enabled': True,
                    'health_reminder_enabled': True,
                    'shichen_reminder_enabled': True,

                    'lunar_display_enabled': True,
                    'volume': 70,
                    'time_font_size': 10,
                    'window_opacity': 100,
                    'music_duration': 15,
                    'health_reminder_interval': 60,
                    'shichen_advance_minutes': 5,
                    'autostart_enabled': False,
                    'default_scale': 60,
                    'camera_enabled': True
                }
                self.apply_settings_immediately(default_settings)
                
                QMessageBox.information(self, "完成", 
                    "系统已恢复默认设置并立即生效！\n"
                    "记事本内容已清除，密码已重置。")
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"重置失败：{str(e)}")
    
    def verify_reset_password(self):
        """验证恢复默认设置的密码"""
        try:
            # 加载密码设置
            password_data = self.load_password_data()
            stored_password = password_data.get('password', '')
            
            if not stored_password:
                # 如果没有设置密码，直接允许重置
                return True
            else:
                # 验证密码
                return self.check_reset_password(stored_password)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"密码验证失败：{str(e)}")
            return False
    
    def load_password_data(self):
        """加载密码数据"""
        try:
            with open('notepad_password.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {'password': ''}
    
    def check_reset_password(self, stored_password):
        """检查恢复默认设置的密码"""
        dialog = QDialog(self)
        dialog.setWindowTitle("🔒 验证身份")
        dialog.setFixedSize(350, 160)
        dialog.setModal(True)
        
        # 设置对话框样式
        dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
                border: 2px solid #FF6666;
                border-radius: 10px;
            }
            QLabel {
                color: #FF6666;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                margin: 3px 0px;
            }
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FF6666;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                padding: 6px;
                margin: 2px 0px;
                min-height: 20px;
                max-height: 25px;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FF6666;
                border-radius: 8px;
                color: #FF6666;
                font-weight: bold;
                font-size: 11px;
                padding: 6px 12px;
                margin: 3px;
                min-width: 65px;
                min-height: 25px;
                max-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FF8888;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("⚠️ 恢复默认设置需要验证密码")
        title_label.setAlignment(Qt.AlignCenter)
        
        # 密码输入
        password_edit = QLineEdit()
        password_edit.setEchoMode(QLineEdit.Password)
        password_edit.setPlaceholderText("输入记事本密码")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        ok_btn = QPushButton("✅ 确定")
        cancel_btn = QPushButton("❌ 取消")
        
        def verify_password():
            import hashlib
            password = password_edit.text().strip()
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            if hashed_password == stored_password:
                dialog.accept()
            else:
                QMessageBox.warning(dialog, "错误", "密码错误！无法执行重置操作。")
                password_edit.clear()
                password_edit.setFocus()
        
        # 回车键验证
        password_edit.returnPressed.connect(verify_password)
        ok_btn.clicked.connect(verify_password)
        cancel_btn.clicked.connect(dialog.reject)
        
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        
        # 添加组件到布局
        layout.addWidget(title_label)
        layout.addSpacing(8)
        layout.addWidget(password_edit)
        layout.addSpacing(10)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 自动聚焦到密码输入框
        password_edit.setFocus()
        
        return dialog.exec_() == QDialog.Accepted
    
    def clear_all_data_files(self):
        """清除所有数据文件"""
        import os
        
        files_to_clear = [
            'notes.txt',                # 记事本内容
            'notepad_password.json',    # 记事本密码
            'schedules.json',           # 日程安排
            'reminders.json',           # 提醒事项
            'alarms.json'               # 闹钟设置
        ]
        
        for file_name in files_to_clear:
            try:
                if os.path.exists(file_name):
                    os.remove(file_name)
            except Exception as e:
                print(f"删除文件 {file_name} 失败：{str(e)}")
    
    def set_autostart(self, enabled):
        """设置开机自启动"""
        try:
            import winreg
            import os
            import sys
            
            # 注册表路径
            reg_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
            app_name = "八卦时钟密码保护版"
            
            # 获取当前程序路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                app_path = sys.executable
            else:
                # 如果是Python脚本
                app_path = os.path.abspath(__file__)
            
            # 打开注册表键
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_ALL_ACCESS)
            
            if enabled:
                # 添加开机自启动
                winreg.SetValueEx(key, app_name, 0, winreg.REG_SZ, app_path)
                print(f"已设置开机自启动：{app_path}")
            else:
                # 删除开机自启动
                try:
                    winreg.DeleteValue(key, app_name)
                    print("已取消开机自启动")
                except FileNotFoundError:
                    # 如果注册表项不存在，忽略错误
                    pass
            
            winreg.CloseKey(key)
            
        except Exception as e:
            print(f"设置开机自启动失败：{str(e)}")
            QMessageBox.warning(self, "警告", f"设置开机自启动失败：{str(e)}")
    
    def check_autostart_status(self):
        """检查开机自启动状态"""
        try:
            import winreg
            
            reg_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
            app_name = "八卦时钟密码保护版"
            
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_READ)
            
            try:
                value, _ = winreg.QueryValueEx(key, app_name)
                winreg.CloseKey(key)
                return True
            except FileNotFoundError:
                winreg.CloseKey(key)
                return False
                
        except Exception as e:
            print(f"检查开机自启动状态失败：{str(e)}")
            return False


class ProfessionalNotepad(QWidget):
    """智能秘书记事本"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("智能秘书 - 传统健康智慧时钟 V1.1.2")
        self.setFixedSize(800, 700)
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        
        # 初始化数据
        self.schedules = []  # 日程安排列表
        self.reminders = []  # 提醒事项列表
        self.password_verified = False  # 密码验证状态
        
        # 先验证密码
        if not self.verify_password():
            self.close()
            return
        
        self.init_ui()
        self.load_notes()
        self.load_schedules()
        
        # 定时器检查提醒
        self.reminder_timer = QTimer()
        self.reminder_timer.timeout.connect(self.check_reminders)
        self.reminder_timer.start(30000)  # 每30秒检查一次
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
            }
            QTabWidget::pane {
                border: 2px solid #FFD700;
                border-radius: 10px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                color: #FFD700;
                font-weight: bold;
                padding: 8px 15px;
                margin: 2px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5A5A5A, stop:1 #3A3A3A);
            }
            QTextEdit, QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 12px;
                padding: 8px;
            }
            QListWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 12px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #555555;
            }
            QListWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                color: #FFA500;
            }
            QDateEdit, QTimeEdit, QComboBox, QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #3A3A3A);
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                padding: 5px;
                min-width: 80px;
            }
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5A5A5A, stop:1 #4A4A4A);
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                padding: 6px;
                min-width: 80px;
                min-height: 25px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border: 1px solid #FFD700;
                width: 16px;
                height: 12px;
                border-radius: 3px;
            }
            QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                margin-right: 2px;
                margin-top: 2px;
            }
            QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                margin-right: 2px;
                margin-bottom: 2px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7A7A7A, stop:1 #5A5A5A);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 1px solid #FFD700;
                width: 8px;
                height: 8px;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 12px;
                color: #FFD700;
                font-weight: bold;
                padding: 8px 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #1A1A1A);
            }
            QLabel {
                color: #FFD700;
                font-weight: bold;
                font-size: 12px;
            }
        """)
    
    def init_ui(self):
        """初始化智能秘书界面"""
        main_layout = QVBoxLayout()
        
        # 标题
        title = QLabel("🤖 智能秘书 - 专业版")
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 20px;
                font-weight: bold;
                padding: 10px;
                background: transparent;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 快速记事选项卡
        self.create_notes_tab()
        
        # 日程管理选项卡
        self.create_schedule_tab()
        
        # 提醒事项选项卡
        self.create_reminder_tab()
        
        # 历史记录选项卡
        self.create_history_tab()
        
        main_layout.addWidget(title)
        main_layout.addWidget(self.tab_widget)
        
        self.setLayout(main_layout)
    
    def verify_password(self):
        """验证密码"""
        try:
            # 加载密码设置
            password_data = self.load_password_data()
            stored_password = password_data.get('password', '')
            
            if not stored_password:
                # 首次使用，设置密码
                return self.set_initial_password()
            else:
                # 验证密码
                return self.check_password(stored_password)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"密码验证失败：{str(e)}")
            return False
    
    def load_password_data(self):
        """加载密码数据"""
        try:
            with open('notepad_password.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {'password': ''}
    
    def save_password_data(self, password):
        """保存密码数据"""
        import hashlib
        # 使用SHA256加密密码
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        password_data = {'password': hashed_password}
        with open('notepad_password.json', 'w', encoding='utf-8') as f:
            json.dump(password_data, f, ensure_ascii=False, indent=2)
    
    def set_initial_password(self):
        """设置初始密码"""
        dialog = QDialog(self)
        dialog.setWindowTitle("🔒 设置记事本密码")
        dialog.setFixedSize(380, 240)
        dialog.setModal(True)
        
        # 设置对话框样式
        dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
                border: 2px solid #FFD700;
                border-radius: 10px;
            }
            QLabel {
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                padding: 2px;
                margin: 1px 0px;
            }
            QLabel#title {
                color: #FFD700;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                margin: 3px 0px;
            }
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                padding: 6px;
                margin: 2px 0px;
                min-height: 20px;
                max-height: 25px;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFD700;
                font-weight: bold;
                font-size: 11px;
                padding: 6px 12px;
                margin: 3px;
                min-width: 65px;
                min-height: 25px;
                max-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(6)
        
        # 标题
        title_label = QLabel("🔒 首次使用，请设置记事本密码")
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignCenter)
        
        # 密码输入区域
        password_label = QLabel("请输入密码：")
        password_edit = QLineEdit()
        password_edit.setEchoMode(QLineEdit.Password)
        password_edit.setPlaceholderText("输入密码（6-20位）")
        
        # 确认密码区域
        confirm_label = QLabel("确认密码：")
        confirm_edit = QLineEdit()
        confirm_edit.setEchoMode(QLineEdit.Password)
        confirm_edit.setPlaceholderText("再次输入密码")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        ok_btn = QPushButton("✅ 确定")
        cancel_btn = QPushButton("❌ 取消")
        
        def set_password():
            password = password_edit.text().strip()
            confirm = confirm_edit.text().strip()
            
            if len(password) < 6 or len(password) > 20:
                QMessageBox.warning(dialog, "警告", "密码长度必须在6-20位之间！")
                return
            
            if password != confirm:
                QMessageBox.warning(dialog, "警告", "两次输入的密码不一致！")
                return
            
            self.save_password_data(password)
            QMessageBox.information(dialog, "成功", "密码设置成功！")
            dialog.accept()
        
        ok_btn.clicked.connect(set_password)
        cancel_btn.clicked.connect(dialog.reject)
        
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        
        # 添加组件到布局
        layout.addWidget(title_label)
        layout.addSpacing(8)
        layout.addWidget(password_label)
        layout.addWidget(password_edit)
        layout.addSpacing(3)
        layout.addWidget(confirm_label)
        layout.addWidget(confirm_edit)
        layout.addSpacing(10)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 自动聚焦到密码输入框
        password_edit.setFocus()
        
        return dialog.exec_() == QDialog.Accepted
    
    def check_password(self, stored_password):
        """检查密码"""
        dialog = QDialog(self)
        dialog.setWindowTitle("🔒 验证密码")
        dialog.setFixedSize(320, 150)
        dialog.setModal(True)
        
        # 设置对话框样式
        dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
                border: 2px solid #FFD700;
                border-radius: 10px;
            }
            QLabel {
                color: #FFD700;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                margin: 3px 0px;
            }
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 6px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                padding: 6px;
                margin: 2px 0px;
                min-height: 20px;
                max-height: 25px;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFD700;
                font-weight: bold;
                font-size: 11px;
                padding: 6px 12px;
                margin: 3px;
                min-width: 65px;
                min-height: 25px;
                max-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("🔒 请输入记事本密码")
        title_label.setAlignment(Qt.AlignCenter)
        
        # 密码输入
        password_edit = QLineEdit()
        password_edit.setEchoMode(QLineEdit.Password)
        password_edit.setPlaceholderText("输入密码")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        ok_btn = QPushButton("✅ 确定")
        cancel_btn = QPushButton("❌ 取消")
        
        def verify_password():
            import hashlib
            password = password_edit.text().strip()
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            if hashed_password == stored_password:
                dialog.accept()
            else:
                QMessageBox.warning(dialog, "错误", "密码错误！")
                password_edit.clear()
                password_edit.setFocus()
        
        # 回车键验证
        password_edit.returnPressed.connect(verify_password)
        ok_btn.clicked.connect(verify_password)
        cancel_btn.clicked.connect(dialog.reject)
        
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        
        # 添加组件到布局
        layout.addWidget(title_label)
        layout.addSpacing(8)
        layout.addWidget(password_edit)
        layout.addSpacing(10)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 自动聚焦到密码输入框
        password_edit.setFocus()
        
        return dialog.exec_() == QDialog.Accepted
    
    def create_notes_tab(self):
        """创建快速记事选项卡"""
        notes_widget = QWidget()
        layout = QVBoxLayout()
        
        # 文本编辑区
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("在这里输入您的快速笔记...")
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 保存笔记")
        save_btn.clicked.connect(self.save_notes)
        
        clear_btn = QPushButton("🗑️ 清空")
        clear_btn.clicked.connect(self.clear_notes)
        
        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(clear_btn)
        btn_layout.addStretch()
        
        layout.addWidget(self.text_edit)
        layout.addLayout(btn_layout)
        
        notes_widget.setLayout(layout)
        self.tab_widget.addTab(notes_widget, "📝 快速记事")
    
    def create_schedule_tab(self):
        """创建日程管理选项卡"""
        schedule_widget = QWidget()
        layout = QVBoxLayout()
        
        # 添加日程区域
        add_layout = QHBoxLayout()
        
        # 日期选择
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # 时间选择
        self.time_edit = QTimeEdit()
        self.time_edit.setTime(QTime.currentTime())
        
        # 事项输入
        self.event_edit = QLineEdit()
        self.event_edit.setPlaceholderText("输入日程事项...")
        
        # 类型选择
        self.type_combo = QComboBox()
        self.type_combo.addItems(["📅 会议", "💊 吃药", "🚗 出行", "📞 电话", "🎯 其他"])
        
        add_btn = QPushButton("➕ 添加日程")
        add_btn.clicked.connect(self.add_schedule)
        
        add_layout.addWidget(QLabel("日期:"))
        add_layout.addWidget(self.date_edit)
        add_layout.addWidget(QLabel("时间:"))
        add_layout.addWidget(self.time_edit)
        add_layout.addWidget(QLabel("类型:"))
        add_layout.addWidget(self.type_combo)
        add_layout.addWidget(self.event_edit)
        add_layout.addWidget(add_btn)
        
        # 日程列表
        self.schedule_list = QListWidget()
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        
        edit_btn = QPushButton("✏️ 编辑")
        edit_btn.clicked.connect(self.edit_schedule)
        
        delete_btn = QPushButton("🗑️ 删除")
        delete_btn.clicked.connect(self.delete_schedule)
        
        btn_layout.addWidget(edit_btn)
        btn_layout.addWidget(delete_btn)
        btn_layout.addStretch()
        
        layout.addLayout(add_layout)
        layout.addWidget(QLabel("📋 日程列表:"))
        layout.addWidget(self.schedule_list)
        layout.addLayout(btn_layout)
        
        schedule_widget.setLayout(layout)
        self.tab_widget.addTab(schedule_widget, "📅 日程管理")
    
    def create_reminder_tab(self):
        """创建提醒事项选项卡"""
        reminder_widget = QWidget()
        layout = QVBoxLayout()
        
        # 添加提醒区域
        add_layout = QHBoxLayout()
        
        # 提醒时间
        self.reminder_time_edit = QTimeEdit()
        self.reminder_time_edit.setTime(QTime.currentTime())
        
        # 提醒内容
        self.reminder_edit = QLineEdit()
        self.reminder_edit.setPlaceholderText("输入提醒内容...")
        
        # 重复选项
        self.repeat_combo = QComboBox()
        self.repeat_combo.addItems(["一次性", "每天", "工作日", "周末"])
        
        # 音乐选择
        self.music_combo = QComboBox()
        self.music_combo.addItems(["🔔 默认提示音", "🎵 轻音乐", "⏰ 闹钟音", "🎶 自定义"])
        
        add_reminder_btn = QPushButton("⏰ 添加提醒")
        add_reminder_btn.clicked.connect(self.add_reminder)
        
        add_layout.addWidget(QLabel("时间:"))
        add_layout.addWidget(self.reminder_time_edit)
        add_layout.addWidget(QLabel("重复:"))
        add_layout.addWidget(self.repeat_combo)
        add_layout.addWidget(QLabel("音乐:"))
        add_layout.addWidget(self.music_combo)
        add_layout.addWidget(self.reminder_edit)
        add_layout.addWidget(add_reminder_btn)
        
        # 提醒列表
        self.reminder_list = QListWidget()
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        
        test_btn = QPushButton("🔊 测试音乐")
        test_btn.clicked.connect(self.test_music)
        
        edit_reminder_btn = QPushButton("✏️ 编辑")
        edit_reminder_btn.clicked.connect(self.edit_reminder)
        
        delete_reminder_btn = QPushButton("🗑️ 删除")
        delete_reminder_btn.clicked.connect(self.delete_reminder)
        
        btn_layout.addWidget(test_btn)
        btn_layout.addWidget(edit_reminder_btn)
        btn_layout.addWidget(delete_reminder_btn)
        btn_layout.addStretch()
        
        layout.addLayout(add_layout)
        layout.addWidget(QLabel("⏰ 提醒列表:"))
        layout.addWidget(self.reminder_list)
        layout.addLayout(btn_layout)
        
        reminder_widget.setLayout(layout)
        self.tab_widget.addTab(reminder_widget, "⏰ 提醒事项")
    
    def create_history_tab(self):
        """创建历史记录选项卡"""
        history_widget = QWidget()
        layout = QVBoxLayout()
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索历史记录...")
        
        search_btn = QPushButton("🔍 搜索")
        search_btn.clicked.connect(self.search_history)
        
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_history)
        
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(search_btn)
        search_layout.addWidget(refresh_btn)
        
        # 历史记录列表
        self.history_list = QListWidget()
        
        # 详情显示
        self.history_detail = QTextEdit()
        self.history_detail.setReadOnly(True)
        self.history_detail.setMaximumHeight(150)
        
        layout.addLayout(search_layout)
        layout.addWidget(QLabel("📚 历史记录:"))
        layout.addWidget(self.history_list)
        layout.addWidget(QLabel("📖 详情:"))
        layout.addWidget(self.history_detail)
        
        history_widget.setLayout(layout)
        self.tab_widget.addTab(history_widget, "📚 历史记录")
        
        # 连接信号
        self.history_list.itemClicked.connect(self.show_history_detail)
    
    def save_notes(self):
        """保存笔记"""
        try:
            with open('notes.txt', 'w', encoding='utf-8') as f:
                f.write(self.text_edit.toPlainText())
            QMessageBox.information(self, "保存成功", "笔记已保存到 notes.txt")
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存失败：{str(e)}")
    
    def load_notes(self):
        """加载笔记"""
        try:
            with open('notes.txt', 'r', encoding='utf-8') as f:
                self.text_edit.setPlainText(f.read())
        except:
            pass
    
    def clear_notes(self):
        """清空笔记"""
        reply = QMessageBox.question(self, "确认清空", "确定要清空所有笔记吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.text_edit.clear()
    
    def add_schedule(self):
        """添加日程"""
        date = self.date_edit.date().toString("yyyy-MM-dd")
        time = self.time_edit.time().toString("HH:mm")
        event_type = self.type_combo.currentText()
        event = self.event_edit.text().strip()
        
        if not event:
            QMessageBox.warning(self, "警告", "请输入日程事项！")
            return
        
        schedule_item = {
            'id': len(self.schedules),
            'date': date,
            'time': time,
            'type': event_type,
            'event': event,
            'created': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.schedules.append(schedule_item)
        self.update_schedule_list()
        self.save_schedules()
        
        # 清空输入
        self.event_edit.clear()
        self.time_edit.setTime(QTime.currentTime())
        
        QMessageBox.information(self, "成功", "日程已添加！")
    
    def edit_schedule(self):
        """编辑日程"""
        current_row = self.schedule_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要编辑的日程！")
            return
        
        schedule = self.schedules[current_row]
        
        # 创建编辑对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("编辑日程")
        dialog.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # 编辑控件
        date_edit = QDateEdit()
        date_edit.setDate(QDate.fromString(schedule['date'], "yyyy-MM-dd"))
        
        time_edit = QTimeEdit()
        time_edit.setTime(QTime.fromString(schedule['time'], "HH:mm"))
        
        type_combo = QComboBox()
        type_combo.addItems(["📅 会议", "💊 吃药", "🚗 出行", "📞 电话", "🎯 其他"])
        type_combo.setCurrentText(schedule['type'])
        
        event_edit = QLineEdit(schedule['event'])
        
        # 按钮
        btn_layout = QHBoxLayout()
        save_btn = QPushButton("保存")
        cancel_btn = QPushButton("取消")
        
        def save_changes():
            self.schedules[current_row].update({
                'date': date_edit.date().toString("yyyy-MM-dd"),
                'time': time_edit.time().toString("HH:mm"),
                'type': type_combo.currentText(),
                'event': event_edit.text().strip()
            })
            self.update_schedule_list()
            self.save_schedules()
            dialog.accept()
            QMessageBox.information(self, "成功", "日程已更新！")
        
        save_btn.clicked.connect(save_changes)
        cancel_btn.clicked.connect(dialog.reject)
        
        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(cancel_btn)
        
        layout.addWidget(QLabel("日期:"))
        layout.addWidget(date_edit)
        layout.addWidget(QLabel("时间:"))
        layout.addWidget(time_edit)
        layout.addWidget(QLabel("类型:"))
        layout.addWidget(type_combo)
        layout.addWidget(QLabel("事项:"))
        layout.addWidget(event_edit)
        layout.addLayout(btn_layout)
        
        dialog.setLayout(layout)
        dialog.exec_()
    
    def delete_schedule(self):
        """删除日程"""
        current_row = self.schedule_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的日程！")
            return
        
        reply = QMessageBox.question(self, "确认删除", "确定要删除这个日程吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            del self.schedules[current_row]
            # 重新分配ID
            for i, schedule in enumerate(self.schedules):
                schedule['id'] = i
            self.update_schedule_list()
            self.save_schedules()
            QMessageBox.information(self, "成功", "日程已删除！")
    
    def add_reminder(self):
        """添加提醒"""
        time = self.reminder_time_edit.time().toString("HH:mm")
        content = self.reminder_edit.text().strip()
        repeat = self.repeat_combo.currentText()
        music = self.music_combo.currentText()
        
        if not content:
            QMessageBox.warning(self, "警告", "请输入提醒内容！")
            return
        
        reminder_item = {
            'id': len(self.reminders),
            'time': time,
            'content': content,
            'repeat': repeat,
            'music': music,
            'enabled': True,
            'created': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.reminders.append(reminder_item)
        self.update_reminder_list()
        self.save_reminders()
        
        # 清空输入
        self.reminder_edit.clear()
        self.reminder_time_edit.setTime(QTime.currentTime())
        
        QMessageBox.information(self, "成功", "提醒已添加！")
    
    def edit_reminder(self):
        """编辑提醒"""
        current_row = self.reminder_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要编辑的提醒！")
            return
        
        # 实现编辑提醒的逻辑（类似编辑日程）
        QMessageBox.information(self, "提示", "编辑提醒功能开发中...")
    
    def delete_reminder(self):
        """删除提醒"""
        current_row = self.reminder_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的提醒！")
            return
        
        reply = QMessageBox.question(self, "确认删除", "确定要删除这个提醒吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            del self.reminders[current_row]
            # 重新分配ID
            for i, reminder in enumerate(self.reminders):
                reminder['id'] = i
            self.update_reminder_list()
            self.save_reminders()
            QMessageBox.information(self, "成功", "提醒已删除！")
    
    def test_music(self):
        """测试音乐"""
        music_type = self.music_combo.currentText()
        try:
            # 加载系统设置
            settings = self.load_system_settings()
            default_music_path = settings.get('default_music_path', 'C:/Users/<USER>/Desktop/金光咒.mp3')
            volume = settings.get('volume', 70) / 100.0
            
            if "默认提示音" in music_type:
                # 播放系统提示音
                import winsound
                winsound.MessageBeep(winsound.MB_ICONASTERISK)  # 修复：使用MB_ICONASTERISK替代MB_ICONINFORMATION
            elif "轻音乐" in music_type:
                # 播放轻音乐（使用默认音乐路径）
                pygame.mixer.music.load(default_music_path)
                pygame.mixer.music.set_volume(volume * 0.7)  # 轻音乐音量稍小
                pygame.mixer.music.play()
            elif "闹钟音" in music_type:
                # 播放闹钟音（使用默认音乐路径）
                pygame.mixer.music.load(default_music_path)
                pygame.mixer.music.set_volume(volume)
                pygame.mixer.music.play()
            else:
                # 自定义音乐（使用默认音乐路径）
                pygame.mixer.music.load(default_music_path)
                pygame.mixer.music.set_volume(volume)
                pygame.mixer.music.play()
                
            QMessageBox.information(self, "测试", f"正在播放：{music_type}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法播放音乐：{str(e)}")
    
    def load_system_settings(self):
        """加载系统设置"""
        try:
            with open('system_settings.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {
                'default_music_path': 'C:/Users/<USER>/Desktop/金光咒.mp3',
                'notepad_enabled': True,
                'alarm_enabled': True,
                'health_reminder_enabled': True,
                'volume': 70
            }
    
    def search_history(self):
        """搜索历史记录"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.refresh_history()
            return
        
        # 实现搜索逻辑
        QMessageBox.information(self, "提示", f"搜索功能开发中...搜索词：{search_text}")
    
    def refresh_history(self):
        """刷新历史记录"""
        self.history_list.clear()
        
        # 加载历史记录
        try:
            # 加载日程历史
            for schedule in self.schedules:
                item_text = f"📅 {schedule['date']} {schedule['time']} - {schedule['type']} - {schedule['event']}"
                self.history_list.addItem(item_text)
            
            # 加载提醒历史
            for reminder in self.reminders:
                item_text = f"⏰ {reminder['time']} - {reminder['content']} ({reminder['repeat']})"
                self.history_list.addItem(item_text)
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载历史记录失败：{str(e)}")
    
    def show_history_detail(self, item):
        """显示历史详情"""
        item_text = item.text()
        # 根据选中的项目显示详细信息
        self.history_detail.setText(f"详细信息：\n{item_text}\n\n创建时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def update_schedule_list(self):
        """更新日程列表"""
        self.schedule_list.clear()
        for schedule in self.schedules:
            item_text = f"{schedule['date']} {schedule['time']} - {schedule['type']} - {schedule['event']}"
            self.schedule_list.addItem(item_text)
    
    def update_reminder_list(self):
        """更新提醒列表"""
        self.reminder_list.clear()
        for reminder in self.reminders:
            status = "✅" if reminder['enabled'] else "❌"
            item_text = f"{status} {reminder['time']} - {reminder['content']} ({reminder['repeat']})"
            self.reminder_list.addItem(item_text)
    
    def load_schedules(self):
        """加载日程数据"""
        try:
            with open('schedules.json', 'r', encoding='utf-8') as f:
                self.schedules = json.load(f)
            self.update_schedule_list()
        except:
            self.schedules = []
        
        try:
            with open('reminders.json', 'r', encoding='utf-8') as f:
                self.reminders = json.load(f)
            self.update_reminder_list()
        except:
            self.reminders = []
    
    def save_schedules(self):
        """保存日程数据"""
        with open('schedules.json', 'w', encoding='utf-8') as f:
            json.dump(self.schedules, f, ensure_ascii=False, indent=2)
    
    def save_reminders(self):
        """保存提醒数据"""
        with open('reminders.json', 'w', encoding='utf-8') as f:
            json.dump(self.reminders, f, ensure_ascii=False, indent=2)
    
    def check_reminders(self):
        """检查提醒事项"""
        current_time = QTime.currentTime()
        current_day = QDate.currentDate().dayOfWeek()  # 1=Monday, 7=Sunday
        
        for reminder in self.reminders:
            if not reminder['enabled']:
                continue
                
            reminder_time = QTime.fromString(reminder['time'], "HH:mm")
            
            # 检查时间是否匹配（允许1分钟误差）
            time_diff = abs(current_time.secsTo(reminder_time))
            if time_diff > 60:  # 超过1分钟就跳过
                continue
            
            # 检查重复规则
            repeat_type = reminder['repeat']
            should_remind = False
            
            if repeat_type == "一次性":
                should_remind = True
            elif repeat_type == "每天":
                should_remind = True
            elif repeat_type == "工作日" and current_day <= 5:
                should_remind = True
            elif repeat_type == "周末" and current_day > 5:
                should_remind = True
            
            if should_remind:
                self.show_reminder_popup(reminder)
    
    def show_reminder_popup(self, reminder):
        """显示提醒弹窗"""
        # 播放音乐
        try:
            # 加载系统设置
            settings = self.load_system_settings()
            default_music_path = settings.get('default_music_path', 'C:/Users/<USER>/Desktop/金光咒.mp3')
            volume = settings.get('volume', 70) / 100.0
            
            music_type = reminder['music']
            if "默认提示音" in music_type:
                import winsound
                winsound.MessageBeep(winsound.MB_ICONASTERISK)  # 修复：使用MB_ICONASTERISK替代MB_ICONINFORMATION
            elif "轻音乐" in music_type:
                pygame.mixer.music.load(default_music_path)
                pygame.mixer.music.set_volume(volume * 0.7)
                pygame.mixer.music.play()
            elif "闹钟音" in music_type:
                pygame.mixer.music.load(default_music_path)
                pygame.mixer.music.set_volume(volume)
                pygame.mixer.music.play()
            else:
                pygame.mixer.music.load(default_music_path)
                pygame.mixer.music.set_volume(volume)
                pygame.mixer.music.play()
        except:
            pass
        
        # 显示提醒对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("⏰ 智能提醒")
        msg_box.setText(f"🔔 提醒时间到了！\n\n📝 {reminder['content']}")
        msg_box.setIcon(QMessageBox.Information)
        
        # 自定义按钮
        later_btn = msg_box.addButton("5分钟后提醒", QMessageBox.ActionRole)
        done_btn = msg_box.addButton("已完成", QMessageBox.AcceptRole)
        
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
                border: 2px solid #FFD700;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 15px;
                color: #FFD700;
                font-weight: bold;
                padding: 10px;
                min-width: 100px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
        
        result = msg_box.exec_()
        
        # 停止音乐
        try:
            pygame.mixer.music.stop()
        except:
            pass


class ProfessionalAlarmSettings(QWidget):
    """专业闹钟设置"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("闹钟设置 - 传统健康智慧时钟 V1.1.2")
        self.setFixedSize(500, 600)
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint | Qt.WindowCloseButtonHint)
        
        self.alarms = []
        self.init_ui()
        self.load_alarms()
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2A2A2A, stop:1 #1A1A1A);
                color: #FFD700;
            }
            QListWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 10px;
                color: #FFFFFF;
                font-size: 14px;
                padding: 10px;
            }
            QTimeEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 14px;
                padding: 8px;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A4A4A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 15px;
                color: #FFD700;
                font-weight: bold;
                padding: 10px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6A6A6A, stop:1 #4A4A4A);
                border-color: #FFA500;
            }
        """)
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("⏰ 闹钟设置 - 专业版")
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 20px;
                font-weight: bold;
                padding: 10px;
                background: transparent;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        
        # 时间设置区域
        time_group = QGroupBox("🕐 时间设置")
        time_group.setStyleSheet("""
            QGroupBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 10px;
                color: #FFD700;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                margin-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        time_layout = QVBoxLayout()
        
        # 小时设置
        hour_layout = QHBoxLayout()
        hour_layout.addWidget(QLabel("小时:"))
        
        self.hour_display = QLabel("12")
        self.hour_display.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
                min-width: 60px;
                text-align: center;
            }
        """)
        self.hour_display.setAlignment(Qt.AlignCenter)
        self.current_hour = 12
        
        hour_btn_layout = QVBoxLayout()
        hour_up_btn = QPushButton("▲")
        hour_up_btn.setFixedSize(30, 25)
        hour_up_btn.clicked.connect(self.hour_up)
        
        hour_down_btn = QPushButton("▼")
        hour_down_btn.setFixedSize(30, 25)
        hour_down_btn.clicked.connect(self.hour_down)
        
        hour_btn_layout.addWidget(hour_up_btn)
        hour_btn_layout.addWidget(hour_down_btn)
        
        hour_layout.addWidget(self.hour_display)
        hour_layout.addLayout(hour_btn_layout)
        hour_layout.addStretch()
        
        # 分钟设置
        minute_layout = QHBoxLayout()
        minute_layout.addWidget(QLabel("分钟:"))
        
        self.minute_display = QLabel("00")
        self.minute_display.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A3A3A, stop:1 #2A2A2A);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
                min-width: 60px;
                text-align: center;
            }
        """)
        self.minute_display.setAlignment(Qt.AlignCenter)
        self.current_minute = 0
        
        minute_btn_layout = QVBoxLayout()
        minute_up_btn = QPushButton("▲")
        minute_up_btn.setFixedSize(30, 25)
        minute_up_btn.clicked.connect(self.minute_up)
        
        minute_down_btn = QPushButton("▼")
        minute_down_btn.setFixedSize(30, 25)
        minute_down_btn.clicked.connect(self.minute_down)
        
        minute_btn_layout.addWidget(minute_up_btn)
        minute_btn_layout.addWidget(minute_down_btn)
        
        minute_layout.addWidget(self.minute_display)
        minute_layout.addLayout(minute_btn_layout)
        minute_layout.addStretch()
        
        time_layout.addLayout(hour_layout)
        time_layout.addLayout(minute_layout)
        time_group.setLayout(time_layout)
        
        # 备注输入
        note_layout = QHBoxLayout()
        note_layout.addWidget(QLabel("备注:"))
        self.note_edit = QLineEdit()
        self.note_edit.setPlaceholderText("闹钟备注...")
        note_layout.addWidget(self.note_edit)
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        
        add_btn = QPushButton("➕ 添加闹钟")
        add_btn.clicked.connect(self.add_alarm)
        
        delete_btn = QPushButton("🗑️ 删除闹钟")
        delete_btn.clicked.connect(self.delete_alarm)
        
        btn_layout.addWidget(add_btn)
        btn_layout.addWidget(delete_btn)
        btn_layout.addStretch()
        
        # 闹钟列表
        self.alarm_list = QListWidget()
        
        layout.addWidget(title)
        layout.addWidget(time_group)
        layout.addLayout(note_layout)
        layout.addLayout(btn_layout)
        layout.addWidget(QLabel("📋 闹钟列表:"))
        layout.addWidget(self.alarm_list)
        
        self.setLayout(layout)
    
    def hour_up(self):
        """小时增加"""
        self.current_hour = (self.current_hour % 24) + 1
        if self.current_hour == 25:
            self.current_hour = 1
        self.hour_display.setText(f"{self.current_hour:02d}")
    
    def hour_down(self):
        """小时减少"""
        self.current_hour = self.current_hour - 1
        if self.current_hour < 1:
            self.current_hour = 24
        self.hour_display.setText(f"{self.current_hour:02d}")
    
    def minute_up(self):
        """分钟增加"""
        self.current_minute = (self.current_minute + 1) % 60
        self.minute_display.setText(f"{self.current_minute:02d}")
    
    def minute_down(self):
        """分钟减少"""
        self.current_minute = (self.current_minute - 1) % 60
        self.minute_display.setText(f"{self.current_minute:02d}")
    
    def add_alarm(self):
        """添加闹钟"""
        # 从显示器获取时间
        time_str = f"{self.current_hour:02d}:{self.current_minute:02d}"
        note = self.note_edit.text().strip()
        
        if not note:
            note = f"闹钟提醒 - {time_str}"
        
        alarm = {
            'time': time_str,
            'note': note,
            'triggered': False,
            'created': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.alarms.append(alarm)
        self.update_alarm_list()
        self.save_alarms()
        
        self.note_edit.clear()
        QMessageBox.information(self, "成功", f"闹钟已添加：{time_str}\n备注：{note}")
    
    def delete_alarm(self):
        """删除闹钟"""
        current_row = self.alarm_list.currentRow()
        if current_row >= 0:
            del self.alarms[current_row]
            self.update_alarm_list()
            self.save_alarms()
            QMessageBox.information(self, "成功", "闹钟已删除")
        else:
            QMessageBox.warning(self, "错误", "请选择要删除的闹钟")
    
    def update_alarm_list(self):
        """更新闹钟列表"""
        self.alarm_list.clear()
        for alarm in self.alarms:
            display_text = f"{alarm['time']} - {alarm.get('note', '无备注')}"
            self.alarm_list.addItem(display_text)
    
    def load_alarms(self):
        """加载闹钟"""
        try:
            with open('alarms.json', 'r', encoding='utf-8') as f:
                self.alarms = json.load(f)
            self.update_alarm_list()
        except:
            self.alarms = []
    
    def save_alarms(self):
        """保存闹钟"""
        with open('alarms.json', 'w', encoding='utf-8') as f:
            json.dump(self.alarms, f, ensure_ascii=False, indent=2)


def main():
    """主函数（无托盘，保证主窗口显示）"""
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("中医健康时钟-专业版本")
        app.setApplicationVersion("V1.1")
        app.setOrganizationName("湖南全航信息通信有限公司")
        app.setOrganizationDomain("quanhang.com")
        
        # 🔥 修复：添加异常处理，防止闪退
        print("正在启动传统健康智慧时钟 V1.1.2...")
        
        # 创建主窗口
        main_window = ProfessionalBaguaClock()
        main_window.show()
        
        print("主窗口已创建并显示")
        
        # 直接进入主事件循环，无托盘
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"程序启动失败：{str(e)}")
        import traceback
        traceback.print_exc()
        # 显示错误对话框
        try:
            error_app = QApplication(sys.argv) if 'app' not in locals() else app
            QMessageBox.critical(None, "启动错误", f"程序启动失败：\n{str(e)}\n\n请检查系统环境和依赖库。")
        except:
            pass


if __name__ == "__main__":
    main() 