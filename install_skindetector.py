#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SkinDetector自动安装脚本
"""

import subprocess
import sys
import os
import json
from datetime import datetime

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name, "--upgrade"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出错: {e}")
        return False

def create_local_skindetector():
    """创建本地SkinDetector实现"""
    try:
        # 创建skindetector目录
        os.makedirs("skindetector", exist_ok=True)
        
        # 创建__init__.py
        with open("skindetector/__init__.py", "w", encoding="utf-8") as f:
            f.write('from .skindetector import SkinDetector\n')
        
        # 创建主模块
        skindetector_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地SkinDetector实现
"""

import cv2
import numpy as np
from typing import Optional, Tuple

class SkinDetector:
    """本地皮肤检测器实现"""
    
    def __init__(self):
        """初始化皮肤检测器"""
        self.lower_skin = np.array([0, 20, 70], dtype=np.uint8)
        self.upper_skin = np.array([20, 255, 255], dtype=np.uint8)
        
        # 额外的皮肤色彩范围
        self.lower_skin2 = np.array([170, 20, 70], dtype=np.uint8)
        self.upper_skin2 = np.array([180, 255, 255], dtype=np.uint8)
    
    def detect(self, image: np.ndarray) -> np.ndarray:
        """检测皮肤区域"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 创建皮肤掩码
            mask1 = cv2.inRange(hsv, self.lower_skin, self.upper_skin)
            mask2 = cv2.inRange(hsv, self.lower_skin2, self.upper_skin2)
            
            # 合并掩码
            skin_mask = cv2.bitwise_or(mask1, mask2)
            
            # 形态学操作
            kernel = np.ones((3, 3), np.uint8)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
            
            return skin_mask
            
        except Exception as e:
            print(f"❌ 皮肤检测失败: {e}")
            return np.zeros(image.shape[:2], dtype=np.uint8)
    
    def detect_regions(self, image: np.ndarray) -> list:
        """检测皮肤区域并返回轮廓"""
        try:
            skin_mask = self.detect(image)
            
            # 查找轮廓
            contours, _ = cv2.findContours(skin_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤小区域
            min_area = 1000
            valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
            
            return valid_contours
            
        except Exception as e:
            print(f"❌ 区域检测失败: {e}")
            return []
    
    def get_skin_percentage(self, image: np.ndarray) -> float:
        """获取皮肤覆盖率"""
        try:
            skin_mask = self.detect(image)
            total_pixels = image.shape[0] * image.shape[1]
            skin_pixels = np.sum(skin_mask > 0)
            
            return skin_pixels / total_pixels if total_pixels > 0 else 0.0
            
        except Exception as e:
            print(f"❌ 覆盖率计算失败: {e}")
            return 0.0
'''
        
        with open("skindetector/skindetector.py", "w", encoding="utf-8") as f:
            f.write(skindetector_code)
        
        print("✅ 本地SkinDetector实现创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 创建本地实现失败: {e}")
        return False

def test_skindetector():
    """测试SkinDetector是否可用"""
    try:
        from skindetector import SkinDetector
        
        # 创建测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:, :] = [255, 200, 150]  # 模拟皮肤颜色
        
        # 测试检测
        detector = SkinDetector()
        mask = detector.detect(test_image)
        
        print("✅ SkinDetector测试成功")
        return True
        
    except ImportError:
        print("❌ SkinDetector导入失败")
        return False
    except Exception as e:
        print(f"❌ SkinDetector测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 SkinDetector自动安装程序")
    print("=" * 50)
    
    # 检查基础环境
    if not check_python_version():
        return False
    
    # 安装依赖包
    dependencies = [
        "opencv-python",
        "numpy",
        "scikit-image",
        "matplotlib"
    ]
    
    print("📦 安装依赖包...")
    for dep in dependencies:
        install_package(dep)
    
    # 创建本地SkinDetector实现
    print("📦 创建本地SkinDetector实现...")
    if create_local_skindetector():
        print("✅ SkinDetector安装完成")
        
        # 测试安装
        if test_skindetector():
            print("✅ SkinDetector功能正常")
            print("🎉 安装完成！现在可以集成到您的健康监测系统中了。")
        else:
            print("⚠️ SkinDetector测试失败，但可能仍可使用")
    else:
        print("❌ SkinDetector安装失败")

if __name__ == "__main__":
    main() 