#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SkinDetector集成模块 - 增强现有健康监测系统
Enhanced Skin Detection Integration for Health Monitor System
"""

import cv2
import numpy as np
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any

try:
    from skindetector import SkinDetector
    SKINDETECTOR_AVAILABLE = True
    print("✅ [SkinDetector] 模块加载成功")
except ImportError:
    SKINDETECTOR_AVAILABLE = False
    print("⚠️ [SkinDetector] 模块未安装，将使用备用检测方法")

class EnhancedSkinDetector:
    """增强皮肤检测器 - 集成SkinDetector与现有系统"""
    
    def __init__(self, config: Dict = None):
        """初始化增强皮肤检测器"""
        self.config = config or {}
        self.skin_detector = None
        self.detection_history = []
        self.analysis_cache = {}
        
        # 初始化SkinDetector
        self._initialize_skin_detector()
        
        # 检测参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
        self.min_skin_area = self.config.get('min_skin_area', 1000)
        self.max_skin_area = self.config.get('max_skin_area', 50000)
        
        print("🔧 [增强皮肤检测] 初始化完成")
    
    def _initialize_skin_detector(self):
        """初始化SkinDetector"""
        try:
            if SKINDETECTOR_AVAILABLE:
                self.skin_detector = SkinDetector()
                print("✅ [SkinDetector] 初始化成功")
            else:
                print("⚠️ [SkinDetector] 使用备用检测方法")
                self.skin_detector = None
        except Exception as e:
            print(f"❌ [SkinDetector] 初始化失败: {e}")
            self.skin_detector = None
    
    def detect_skin_regions(self, frame: np.ndarray) -> Dict[str, Any]:
        """检测皮肤区域 - 主要检测方法"""
        try:
            if self.skin_detector is not None:
                return self._detect_with_skindetector(frame)
            else:
                return self._detect_with_opencv(frame)
        except Exception as e:
            print(f"❌ [皮肤检测] 检测失败: {e}")
            return self._get_fallback_detection()
    
    def _detect_with_skindetector(self, frame: np.ndarray) -> Dict[str, Any]:
        """使用SkinDetector进行皮肤检测"""
        try:
            # 使用SkinDetector检测皮肤
            skin_mask = self.skin_detector.detect(frame)
            
            # 分析检测结果
            skin_regions = self._analyze_skin_regions(frame, skin_mask)
            
            # 计算皮肤健康指标
            health_metrics = self._calculate_skin_health_metrics(frame, skin_mask)
            
            # 生成检测报告
            detection_report = {
                "detection_method": "skindetector",
                "skin_regions": skin_regions,
                "health_metrics": health_metrics,
                "confidence": self._calculate_detection_confidence(skin_mask),
                "timestamp": datetime.now().isoformat(),
                "frame_shape": frame.shape
            }
            
            # 缓存结果
            self._cache_detection_result(detection_report)
            
            print(f"✅ [SkinDetector] 检测完成 - 皮肤区域: {len(skin_regions)}个")
            return detection_report
            
        except Exception as e:
            print(f"❌ [SkinDetector] 检测失败: {e}")
            return self._detect_with_opencv(frame)
    
    def _detect_with_opencv(self, frame: np.ndarray) -> Dict[str, Any]:
        """使用OpenCV进行备用皮肤检测"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # 定义皮肤色彩范围
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            
            # 创建皮肤掩码
            skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # 形态学操作改善掩码
            kernel = np.ones((3, 3), np.uint8)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
            
            # 分析皮肤区域
            skin_regions = self._analyze_skin_regions(frame, skin_mask)
            health_metrics = self._calculate_skin_health_metrics(frame, skin_mask)
            
            detection_report = {
                "detection_method": "opencv_fallback",
                "skin_regions": skin_regions,
                "health_metrics": health_metrics,
                "confidence": self._calculate_detection_confidence(skin_mask),
                "timestamp": datetime.now().isoformat(),
                "frame_shape": frame.shape
            }
            
            self._cache_detection_result(detection_report)
            
            print(f"✅ [OpenCV备用] 检测完成 - 皮肤区域: {len(skin_regions)}个")
            return detection_report
            
        except Exception as e:
            print(f"❌ [OpenCV备用] 检测失败: {e}")
            return self._get_fallback_detection()
    
    def _analyze_skin_regions(self, frame: np.ndarray, skin_mask: np.ndarray) -> List[Dict]:
        """分析检测到的皮肤区域"""
        try:
            # 查找轮廓
            contours, _ = cv2.findContours(skin_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            skin_regions = []
            
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                
                # 过滤太小或太大的区域
                if area < self.min_skin_area or area > self.max_skin_area:
                    continue
                
                # 获取边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 计算区域特征
                region_features = self._calculate_region_features(frame, contour, skin_mask)
                
                skin_region = {
                    "id": i,
                    "area": int(area),
                    "bbox": [x, y, w, h],
                    "features": region_features,
                    "confidence": self._calculate_region_confidence(area, region_features)
                }
                
                skin_regions.append(skin_region)
            
            return skin_regions
            
        except Exception as e:
            print(f"❌ [区域分析] 失败: {e}")
            return []
    
    def _calculate_region_features(self, frame: np.ndarray, contour: np.ndarray, skin_mask: np.ndarray) -> Dict:
        """计算皮肤区域特征"""
        try:
            # 创建区域掩码
            mask = np.zeros(skin_mask.shape, dtype=np.uint8)
            cv2.drawContours(mask, [contour], -1, 255, -1)
            
            # 提取区域
            x, y, w, h = cv2.boundingRect(contour)
            region = frame[y:y+h, x:x+w]
            region_mask = mask[y:y+h, x:x+w]
            
            if region.size == 0:
                return {}
            
            # 计算颜色特征
            mean_color = cv2.mean(region, mask=region_mask)[:3]
            
            # 计算纹理特征
            gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            texture_features = self._calculate_texture_features(gray_region, region_mask)
            
            # 计算均匀性
            uniformity = self._calculate_uniformity(region, region_mask)
            
            return {
                "mean_color": mean_color,
                "texture_features": texture_features,
                "uniformity": uniformity,
                "size": (w, h)
            }
            
        except Exception as e:
            print(f"❌ [特征计算] 失败: {e}")
            return {}
    
    def _calculate_texture_features(self, gray_region: np.ndarray, mask: np.ndarray) -> Dict:
        """计算纹理特征"""
        try:
            # 计算梯度
            grad_x = cv2.Sobel(gray_region, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray_region, cv2.CV_64F, 0, 1, ksize=3)
            
            # 计算梯度幅值
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            
            # 应用掩码
            masked_gradient = gradient_magnitude * (mask / 255.0)
            
            # 计算统计特征
            mean_gradient = np.mean(masked_gradient[mask > 0]) if np.any(mask > 0) else 0
            std_gradient = np.std(masked_gradient[mask > 0]) if np.any(mask > 0) else 0
            
            return {
                "mean_gradient": float(mean_gradient),
                "std_gradient": float(std_gradient),
                "texture_complexity": float(std_gradient / (mean_gradient + 1e-6))
            }
            
        except Exception as e:
            print(f"❌ [纹理特征] 计算失败: {e}")
            return {}
    
    def _calculate_uniformity(self, region: np.ndarray, mask: np.ndarray) -> float:
        """计算皮肤均匀性"""
        try:
            # 转换为灰度
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            
            # 应用掩码
            masked_gray = gray * (mask / 255.0)
            
            # 计算标准差（均匀性指标）
            valid_pixels = masked_gray[mask > 0]
            if len(valid_pixels) == 0:
                return 0.0
            
            std_dev = np.std(valid_pixels)
            mean_val = np.mean(valid_pixels)
            
            # 均匀性 = 1 - (标准差 / 均值)
            uniformity = max(0, 1 - (std_dev / (mean_val + 1e-6)))
            
            return float(uniformity)
            
        except Exception as e:
            print(f"❌ [均匀性] 计算失败: {e}")
            return 0.0
    
    def _calculate_skin_health_metrics(self, frame: np.ndarray, skin_mask: np.ndarray) -> Dict:
        """计算皮肤健康指标"""
        try:
            # 计算皮肤覆盖率
            total_pixels = frame.shape[0] * frame.shape[1]
            skin_pixels = np.sum(skin_mask > 0)
            skin_coverage = skin_pixels / total_pixels
            
            # 计算平均颜色
            masked_frame = frame.copy()
            masked_frame[skin_mask == 0] = 0
            mean_color = cv2.mean(masked_frame, mask=skin_mask)[:3]
            
            # 计算亮度
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            masked_gray = gray * (skin_mask / 255.0)
            brightness = np.mean(masked_gray[skin_mask > 0]) if np.any(skin_mask > 0) else 0
            
            # 计算饱和度
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            saturation = np.mean(hsv[:, :, 1][skin_mask > 0]) if np.any(skin_mask > 0) else 0
            
            return {
                "skin_coverage": float(skin_coverage),
                "mean_color": mean_color,
                "brightness": float(brightness),
                "saturation": float(saturation),
                "total_skin_pixels": int(skin_pixels)
            }
            
        except Exception as e:
            print(f"❌ [健康指标] 计算失败: {e}")
            return {}
    
    def _calculate_detection_confidence(self, skin_mask: np.ndarray) -> float:
        """计算检测置信度"""
        try:
            # 基于皮肤区域大小和连续性计算置信度
            total_pixels = skin_mask.shape[0] * skin_mask.shape[1]
            skin_pixels = np.sum(skin_mask > 0)
            
            if total_pixels == 0:
                return 0.0
            
            coverage_ratio = skin_pixels / total_pixels
            
            # 计算连通性
            num_labels, labels = cv2.connectedComponents(skin_mask)
            connectivity_score = 1.0 / (num_labels + 1)  # 连通区域越少越好
            
            # 综合置信度
            confidence = (coverage_ratio * 0.6 + connectivity_score * 0.4)
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            print(f"❌ [置信度] 计算失败: {e}")
            return 0.5
    
    def _calculate_region_confidence(self, area: float, features: Dict) -> float:
        """计算区域置信度"""
        try:
            # 基于面积和特征质量计算置信度
            area_score = min(1.0, area / 10000)  # 面积分数
            
            # 特征质量分数
            feature_score = 0.5
            if features:
                if 'uniformity' in features:
                    feature_score += features['uniformity'] * 0.3
                if 'texture_features' in features:
                    texture = features['texture_features']
                    if 'texture_complexity' in texture:
                        feature_score += min(1.0, texture['texture_complexity']) * 0.2
            
            confidence = (area_score * 0.7 + feature_score * 0.3)
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            print(f"❌ [区域置信度] 计算失败: {e}")
            return 0.5
    
    def _cache_detection_result(self, result: Dict):
        """缓存检测结果"""
        try:
            # 限制缓存大小
            if len(self.analysis_cache) > 100:
                # 删除最旧的结果
                oldest_key = min(self.analysis_cache.keys())
                del self.analysis_cache[oldest_key]
            
            # 添加新结果
            timestamp = datetime.now().timestamp()
            self.analysis_cache[timestamp] = result
            
            # 添加到历史记录
            self.detection_history.append({
                "timestamp": timestamp,
                "method": result.get("detection_method", "unknown"),
                "confidence": result.get("confidence", 0.0),
                "regions_count": len(result.get("skin_regions", []))
            })
            
            # 限制历史记录大小
            if len(self.detection_history) > 50:
                self.detection_history = self.detection_history[-50:]
                
        except Exception as e:
            print(f"❌ [缓存] 失败: {e}")
    
    def _get_fallback_detection(self) -> Dict[str, Any]:
        """获取备用检测结果"""
        return {
            "detection_method": "fallback",
            "skin_regions": [],
            "health_metrics": {
                "skin_coverage": 0.0,
                "mean_color": (0, 0, 0),
                "brightness": 0.0,
                "saturation": 0.0,
                "total_skin_pixels": 0
            },
            "confidence": 0.0,
            "timestamp": datetime.now().isoformat(),
            "frame_shape": (0, 0, 0)
        }
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        try:
            if not self.detection_history:
                return {"total_detections": 0, "average_confidence": 0.0}
            
            confidences = [h["confidence"] for h in self.detection_history]
            methods = [h["method"] for h in self.detection_history]
            
            return {
                "total_detections": len(self.detection_history),
                "average_confidence": np.mean(confidences),
                "method_distribution": {method: methods.count(method) for method in set(methods)},
                "recent_detections": self.detection_history[-10:]  # 最近10次检测
            }
            
        except Exception as e:
            print(f"❌ [统计] 计算失败: {e}")
            return {"total_detections": 0, "average_confidence": 0.0}
    
    def save_detection_report(self, filename: str = None):
        """保存检测报告"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"skin_detection_report_{timestamp}.json"
            
            report = {
                "detection_statistics": self.get_detection_statistics(),
                "analysis_cache": self.analysis_cache,
                "config": self.config,
                "generated_at": datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"✅ [报告] 已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ [报告] 保存失败: {e}")


# 集成到现有系统的适配器类
class SkinDetectorAdapter:
    """SkinDetector适配器 - 与现有健康监测系统集成"""
    
    def __init__(self, health_monitor_instance):
        """初始化适配器"""
        self.health_monitor = health_monitor_instance
        self.enhanced_detector = EnhancedSkinDetector()
        self.integration_enabled = True
        
        print("🔧 [适配器] SkinDetector适配器初始化完成")
    
    def enhance_skin_analysis(self, face_roi: np.ndarray, existing_analysis: Dict = None) -> Dict[str, Any]:
        """增强现有皮肤分析"""
        try:
            if not self.integration_enabled:
                return existing_analysis or {}
            
            # 使用SkinDetector进行检测
            skin_detection = self.enhanced_detector.detect_skin_regions(face_roi)
            
            # 融合现有分析结果
            enhanced_analysis = self._merge_analysis_results(existing_analysis, skin_detection)
            
            print(f"✅ [适配器] 皮肤分析增强完成 - 检测区域: {len(skin_detection.get('skin_regions', []))}个")
            return enhanced_analysis
            
        except Exception as e:
            print(f"❌ [适配器] 增强分析失败: {e}")
            return existing_analysis or {}
    
    def _merge_analysis_results(self, existing_analysis: Dict, skin_detection: Dict) -> Dict[str, Any]:
        """融合分析结果"""
        try:
            merged = existing_analysis.copy() if existing_analysis else {}
            
            # 添加SkinDetector检测结果
            merged["skindetector_enhanced"] = True
            merged["skin_detection"] = skin_detection
            
            # 融合健康指标
            if "health_metrics" in skin_detection:
                health_metrics = skin_detection["health_metrics"]
                
                # 更新现有指标
                if "skin_coverage" in health_metrics:
                    merged["skin_coverage"] = health_metrics["skin_coverage"]
                
                if "brightness" in health_metrics:
                    merged["brightness_score"] = health_metrics["brightness"] / 255.0
                
                if "saturation" in health_metrics:
                    merged["saturation_score"] = health_metrics["saturation"] / 255.0
            
            # 融合区域信息
            if "skin_regions" in skin_detection:
                regions = skin_detection["skin_regions"]
                if regions:
                    # 计算综合皮肤健康评分
                    avg_confidence = np.mean([r.get("confidence", 0.0) for r in regions])
                    avg_uniformity = np.mean([r.get("features", {}).get("uniformity", 0.0) for r in regions])
                    
                    merged["enhanced_skin_health_score"] = (avg_confidence * 0.6 + avg_uniformity * 0.4) * 100
            
            return merged
            
        except Exception as e:
            print(f"❌ [融合] 结果融合失败: {e}")
            return existing_analysis or {}
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        return {
            "integration_enabled": self.integration_enabled,
            "skindetector_available": SKINDETECTOR_AVAILABLE,
            "detection_statistics": self.enhanced_detector.get_detection_statistics(),
            "config": self.enhanced_detector.config
        }


# 便捷的安装和配置函数
def install_skindetector():
    """安装SkinDetector"""
    try:
        import subprocess
        import sys
        
        print("📦 [安装] 正在安装SkinDetector...")
        
        # 尝试安装SkinDetector
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "skindetector"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ [安装] SkinDetector安装成功")
            return True
        else:
            print(f"❌ [安装] SkinDetector安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ [安装] 安装过程出错: {e}")
        return False


def create_integration_config():
    """创建集成配置文件"""
    config = {
        "skindetector_enabled": True,
        "confidence_threshold": 0.6,
        "min_skin_area": 1000,
        "max_skin_area": 50000,
        "save_detection_reports": True,
        "cache_detection_results": True,
        "integration_method": "enhanced_analysis"
    }
    
    try:
        with open("skindetector_integration_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ [配置] 集成配置文件已创建")
        return config
        
    except Exception as e:
        print(f"❌ [配置] 配置文件创建失败: {e}")
        return config


if __name__ == "__main__":
    # 测试集成
    print("🧪 [测试] SkinDetector集成模块测试")
    
    # 检查SkinDetector是否可用
    if not SKINDETECTOR_AVAILABLE:
        print("⚠️ [测试] SkinDetector未安装，尝试安装...")
        if install_skindetector():
            print("✅ [测试] SkinDetector安装成功，请重启程序")
        else:
            print("⚠️ [测试] SkinDetector安装失败，将使用备用方法")
    
    # 创建配置
    config = create_integration_config()
    print("✅ [测试] 集成模块初始化完成") 