#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCM Knowledge Graph Framework 依赖测试
测试所有必要的依赖包是否正常工作
"""

import sys
import traceback

def test_basic_imports():
    """测试基础导入"""
    print("1. 测试基础Python包...")
    
    try:
        import pandas as pd
        print("✓ pandas 导入成功")
        
        import numpy as np
        print("✓ numpy 导入成功")
        
        import json
        print("✓ json 导入成功")
        
        from tqdm import tqdm
        print("✓ tqdm 导入成功")
        
        import re
        print("✓ re 导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 基础包导入失败: {e}")
        return False

def test_sentence_transformers():
    """测试sentence-transformers"""
    print("\n2. 测试sentence-transformers...")
    
    try:
        from sentence_transformers import SentenceTransformer
        print("✓ sentence_transformers 导入成功")
        
        # 测试创建一个简单的模型（不下载，只测试API）
        print("✓ SentenceTransformer 类可用")
        
        return True
    except Exception as e:
        print(f"✗ sentence_transformers 测试失败: {e}")
        traceback.print_exc()
        return False

def test_transformers():
    """测试transformers"""
    print("\n3. 测试transformers...")
    
    try:
        import transformers
        print(f"✓ transformers 导入成功 (版本: {transformers.__version__})")
        
        from transformers import AutoTokenizer
        print("✓ AutoTokenizer 可用")
        
        return True
    except Exception as e:
        print(f"✗ transformers 测试失败: {e}")
        return False

def test_torch():
    """测试PyTorch"""
    print("\n4. 测试PyTorch...")
    
    try:
        import torch
        print(f"✓ torch 导入成功 (版本: {torch.__version__})")
        
        # 测试基本张量操作
        x = torch.tensor([1, 2, 3])
        y = x + 1
        print(f"✓ 基本张量操作正常: {y.tolist()}")
        
        return True
    except Exception as e:
        print(f"✗ torch 测试失败: {e}")
        return False

def test_scikit_learn():
    """测试scikit-learn"""
    print("\n5. 测试scikit-learn...")
    
    try:
        import sklearn
        print(f"✓ sklearn 导入成功 (版本: {sklearn.__version__})")
        
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        # 测试余弦相似度计算
        a = np.array([[1, 2, 3]])
        b = np.array([[4, 5, 6]])
        similarity = cosine_similarity(a, b)
        print(f"✓ 余弦相似度计算正常: {similarity[0][0]:.4f}")
        
        return True
    except Exception as e:
        print(f"✗ scikit-learn 测试失败: {e}")
        return False

def test_pandas_operations():
    """测试pandas基本操作"""
    print("\n6. 测试pandas数据操作...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        data = {
            'herb_name': ['人参', '黄芪', '当归'],
            'property': ['温', '微温', '温'],
            'flavour': ['甘', '甘', '甘辛']
        }
        
        df = pd.DataFrame(data)
        print(f"✓ DataFrame创建成功: {len(df)} 行")
        
        # 测试字符串操作
        filtered = df[df['herb_name'].str.contains('人')]
        print(f"✓ 字符串过滤操作正常: 找到 {len(filtered)} 条记录")
        
        return True
    except Exception as e:
        print(f"✗ pandas操作测试失败: {e}")
        return False

def test_tcm_project_structure():
    """测试TCM项目结构"""
    print("\n7. 测试TCM项目结构...")
    
    try:
        from pathlib import Path
        
        project_dir = Path("TCM_knowledge_graph-main")
        if not project_dir.exists():
            print("✗ TCM项目目录不存在")
            return False
        
        print("✓ TCM项目目录存在")
        
        # 检查关键目录
        data_dir = project_dir / "data"
        processed_code_dir = project_dir / "processed_code"
        merge_result_dir = project_dir / "merge_result"
        
        if data_dir.exists():
            print("✓ data目录存在")
        else:
            print("✗ data目录不存在")
            
        if processed_code_dir.exists():
            print("✓ processed_code目录存在")
        else:
            print("✗ processed_code目录不存在")
            
        if merge_result_dir.exists():
            print("✓ merge_result目录存在")
        else:
            print("✓ merge_result目录已创建")
        
        return True
    except Exception as e:
        print(f"✗ 项目结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("TCM Knowledge Graph Framework 依赖测试")
    print("="*60)
    
    tests = [
        test_basic_imports,
        test_sentence_transformers,
        test_transformers,
        test_torch,
        test_scikit_learn,
        test_pandas_operations,
        test_tcm_project_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有依赖测试通过！TCM Knowledge Graph Framework 准备就绪！")
        
        print("\n下一步操作建议:")
        print("1. 从各数据库官网下载数据文件")
        print("2. 运行数据处理脚本构建知识图谱")
        print("3. 使用tcm_knowledge_graph_example.py学习框架使用")
        
        return True
    else:
        print("❌ 部分测试失败，请检查依赖安装")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
