{"status": true, "code": 0, "message": "fetch success", "data": {"chart_data": [{"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE003734", "name": "root of common htreewingnut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000027", "name": "taxol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000071", "name": "cordyce<PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000085", "name": "CUCURBITACIN I", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036825", "name": "Honokiol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058053", "name": "diallyl trisulfide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058205", "name": "trans-resveratrol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058408", "name": "naphthalene1", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058519", "name": "(-)-epigallocatechin-3-gallate; (-)-epigallocatechin 3-o-gallate;epigallocatechin gallate; galloyl-l-epigallocatechol; epigallocatechin 3-gallate; (-)-epigallocatechin gallate; ", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN061570", "name": "curcumin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKGE000007", "name": "IL12B", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000024", "name": "NDUFV1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000026", "name": "FYN", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000028", "name": "PHB", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000031", "name": "DNMT3A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000034", "name": "PFKFB3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000041", "name": "IRF1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000045", "name": "IRS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000047", "name": "AKT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000051", "name": "IL1B", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000052", "name": "TNF", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000053", "name": "NFKB1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000059", "name": "NGFR", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000068", "name": "VEGFC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000069", "name": "CCL17", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000070", "name": "CAV1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000072", "name": "CXCL12", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000075", "name": "IL1RN", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000117", "name": "RUNX3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000121", "name": "KCNH2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000130", "name": "TOP2A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000131", "name": "CDC25C", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000135", "name": "CXCR4", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000136", "name": "MKI67", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000137", "name": "PLAU", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000152", "name": "RANBP10", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000158", "name": "RHOA", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000166", "name": "EP300", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000171", "name": "DNMT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000173", "name": "BRAF", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000175", "name": "FBXW7", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000182", "name": "B4galnt2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000196", "name": "FAS", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000199", "name": "ICAM1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000201", "name": "ESR1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000204", "name": "CXCL8", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000205", "name": "NOS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000224", "name": "AGT", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000230", "name": "ADIPOQ", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000233", "name": "DES", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000041", "name": "Isoxanthohumol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN001948", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000004", "name": "Chronic pain", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000006", "name": "Nasopharyngeal Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000022", "name": "Prostatic Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000053", "name": "leukemia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000082", "name": "Nasopharyngeal carcinoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000085", "name": "Inflammation", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000086", "name": "Colonic Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000090", "name": "Acute kidney injury", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE005103", "name": "Himalayan Yew", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE005116", "name": "Japanese Yew", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000149", "name": "MAP3K12", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE001388", "name": "<PERSON><PERSON><PERSON> persi<PERSON>e", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE002788", "name": "<PERSON><PERSON><PERSON> pruni immaturus", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE001037", "name": "MMP2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001073", "name": "PTGS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE004517", "name": "Largeseed Hemsleya", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004640", "name": "Rocket Candytuft", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000727", "name": "MAP1LC3A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000920", "name": "GADD45A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000047", "name": "Neoplasm Metastasis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE002025", "name": "Prepared bark of Officinal magnolia", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE002734", "name": "Magnolia officinalis Rehd. E<PERSON> W<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000118", "name": "CAPN2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000139", "name": "POR", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE002044", "name": "Rice Bean", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004071", "name": "Garlic;Allium sativum", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000017", "name": "MAPT", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE000430", "name": "Caesalpinia", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE000854", "name": "<PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000003", "name": "ASAH1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000005", "name": "UGCG", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE004258", "name": "root of Chinese Angelica", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004583", "name": "English Walnut Seed", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003524", "name": "Emblic Leafflower Fruit", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE005221", "name": "Ginkgo seed;Ginkgo Nut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000002", "name": "CXCL3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE000214", "name": "Zedoray Rhizome", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE000590", "name": "Zedoary Turmeric Equivalent plant: Curcuma k<PERSON>nsis", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000013", "name": "G6PC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000014", "name": "PCK1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN016721", "name": "phorbol diester", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036842", "name": "piperine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN008052", "name": "Cardiolipin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN012700", "name": "coumestrol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000038", "name": "Vomiting", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000080", "name": "Anemia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000450", "name": "Hypertrophic Cardiomyopathy", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN021186", "name": "hexitol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000019", "name": "Parkinson Disease", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000023", "name": "<PERSON><PERSON>, Systemic", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN059684", "name": "Vitamin E β-", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN059746", "name": "α-tocopherol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000017", "name": "Liver Cirrhosis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN035072", "name": "3,4-benzopyrene", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058129", "name": "quercetin-3-O-β-D-glucuronide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000018", "name": "Pancreatic Neoplasm", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000026", "name": "Obesity", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN036067", "name": "vanadium", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036918", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000027", "name": "Cardiovascular Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000031", "name": "Insulin Resistance", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE003726", "name": "Cassia Bark", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003732", "name": "<PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000004", "name": "quercetagetin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000013", "name": "bufalin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE003737", "name": "Common Tobacco", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000029", "name": "colchicine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000047", "name": "Chelerythrine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000001", "name": "sa<PERSON><PERSON>in a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE003724", "name": "rhizome of Common Turmeric", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003730", "name": "<PERSON><PERSON><PERSON> Rhozi<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000008", "name": "ergothioneine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000005", "name": "tetrodotoxin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000015", "name": "<PERSON><PERSON>, Oral", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN036916", "name": "Artemisinin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036835", "name": "w<PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN061952", "name": "Crude fiber", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000012", "name": "Immunosuppression", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN000028", "name": "coumarin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN008636", "name": "acetylcholine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036795", "name": "punicalagin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036801", "name": "enterolactone", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058322", "name": "baicalein", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000006", "name": "evodiamine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000028", "name": "Body mass index", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN000074", "name": "plumbagin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN003665", "name": "evoden", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000023", "name": "acetyl-11-keto-β-boswellicacid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036817", "name": "atractylenolide I", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000030", "name": "cocaine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058127", "name": "quercetin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI003326", "name": "High density lipoprotein measurement", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI007408", "name": "Corpuscular Hemoglobin Concentration Mean", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI007749", "name": "Hematocrit procedure", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI008850", "name": "Malignant neoplasm of stomach", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKIN001521", "name": "Potassium chloride", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN012299", "name": "potassium", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036829", "name": "ursolic acid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000011", "name": "cannabigerol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN057946", "name": "IFP; glycerol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036830", "name": "triptonide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000048", "name": "Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000813", "name": "CAMPOMELIC DYSPLASIA", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE003735", "name": "all-grass of Heartleaf Houttuynia", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000065", "name": "dioscin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036831", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000052", "name": "cannabidiol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036909", "name": "Notoginsenoside R1", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000021", "name": "agrimoniin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE003752", "name": "root of Ligulilobe sage", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN033678", "name": "caffeine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036913", "name": "naringenin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN057911", "name": "se", "record_type": "Ingredients"}], "data_links": [{"source": 0, "target": 1}, {"source": 0, "target": 2}, {"source": 0, "target": 3}, {"source": 0, "target": 4}, {"source": 0, "target": 5}, {"source": 0, "target": 6}, {"source": 0, "target": 7}, {"source": 0, "target": 8}, {"source": 0, "target": 9}, {"source": 0, "target": 10}, {"source": 0, "target": 11}, {"source": 0, "target": 12}, {"source": 0, "target": 13}, {"source": 0, "target": 14}, {"source": 0, "target": 15}, {"source": 0, "target": 16}, {"source": 0, "target": 17}, {"source": 0, "target": 18}, {"source": 0, "target": 19}, {"source": 0, "target": 20}, {"source": 0, "target": 21}, {"source": 0, "target": 22}, {"source": 0, "target": 23}, {"source": 0, "target": 24}, {"source": 0, "target": 25}, {"source": 0, "target": 26}, {"source": 0, "target": 27}, {"source": 0, "target": 28}, {"source": 0, "target": 29}, {"source": 0, "target": 30}, {"source": 0, "target": 31}, {"source": 0, "target": 32}, {"source": 0, "target": 33}, {"source": 0, "target": 34}, {"source": 0, "target": 35}, {"source": 0, "target": 36}, {"source": 0, "target": 37}, {"source": 0, "target": 38}, {"source": 0, "target": 39}, {"source": 0, "target": 40}, {"source": 0, "target": 41}, {"source": 0, "target": 42}, {"source": 0, "target": 43}, {"source": 0, "target": 44}, {"source": 0, "target": 45}, {"source": 0, "target": 46}, {"source": 0, "target": 47}, {"source": 0, "target": 48}, {"source": 0, "target": 49}, {"source": 0, "target": 50}, {"source": 1, "target": 51}, {"source": 1, "target": 52}, {"source": 1, "target": 53}, {"source": 1, "target": 54}, {"source": 1, "target": 55}, {"source": 1, "target": 56}, {"source": 1, "target": 57}, {"source": 1, "target": 58}, {"source": 1, "target": 59}, {"source": 1, "target": 60}, {"source": 1, "target": 61}, {"source": 1, "target": 62}, {"source": 1, "target": 63}, {"source": 1, "target": 64}, {"source": 1, "target": 65}, {"source": 1, "target": 66}, {"source": 2, "target": 67}, {"source": 2, "target": 68}, {"source": 2, "target": 69}, {"source": 3, "target": 70}, {"source": 3, "target": 71}, {"source": 3, "target": 72}, {"source": 3, "target": 73}, {"source": 4, "target": 74}, {"source": 4, "target": 75}, {"source": 4, "target": 76}, {"source": 4, "target": 77}, {"source": 4, "target": 78}, {"source": 5, "target": 79}, {"source": 5, "target": 80}, {"source": 5, "target": 81}, {"source": 5, "target": 82}, {"source": 6, "target": 83}, {"source": 6, "target": 84}, {"source": 6, "target": 85}, {"source": 7, "target": 86}, {"source": 7, "target": 87}, {"source": 7, "target": 88}, {"source": 7, "target": 89}, {"source": 8, "target": 90}, {"source": 8, "target": 91}, {"source": 9, "target": 92}, {"source": 9, "target": 93}, {"source": 9, "target": 94}, {"source": 10, "target": 95}, {"source": 10, "target": 96}, {"source": 10, "target": 97}, {"source": 10, "target": 98}, {"source": 11, "target": 99}, {"source": 11, "target": 100}, {"source": 12, "target": 101}, {"source": 12, "target": 102}, {"source": 12, "target": 103}, {"source": 12, "target": 104}, {"source": 12, "target": 105}, {"source": 13, "target": 106}, {"source": 13, "target": 107}, {"source": 13, "target": 108}, {"source": 14, "target": 109}, {"source": 14, "target": 110}, {"source": 14, "target": 111}, {"source": 15, "target": 112}, {"source": 16, "target": 113}, {"source": 16, "target": 114}, {"source": 16, "target": 115}, {"source": 17, "target": 116}, {"source": 18, "target": 117}, {"source": 18, "target": 118}, {"source": 18, "target": 119}, {"source": 19, "target": 120}, {"source": 19, "target": 121}, {"source": 19, "target": 122}, {"source": 19, "target": 123}, {"source": 20, "target": 124}, {"source": 20, "target": 125}, {"source": 20, "target": 126}, {"source": 21, "target": 127}, {"source": 22, "target": 128}, {"source": 22, "target": 129}, {"source": 22, "target": 130}, {"source": 23, "target": 131}, {"source": 23, "target": 132}, {"source": 24, "target": 133}, {"source": 25, "target": 134}, {"source": 25, "target": 135}, {"source": 25, "target": 136}, {"source": 27, "target": 137}, {"source": 27, "target": 138}, {"source": 28, "target": 139}, {"source": 28, "target": 140}, {"source": 29, "target": 141}, {"source": 30, "target": 142}, {"source": 30, "target": 143}, {"source": 31, "target": 144}, {"source": 31, "target": 145}, {"source": 32, "target": 146}, {"source": 32, "target": 147}, {"source": 33, "target": 148}, {"source": 36, "target": 149}, {"source": 36, "target": 150}, {"source": 36, "target": 151}, {"source": 36, "target": 152}, {"source": 36, "target": 153}, {"source": 37, "target": 154}, {"source": 37, "target": 155}, {"source": 38, "target": 156}, {"source": 39, "target": 157}, {"source": 41, "target": 158}, {"source": 42, "target": 159}, {"source": 42, "target": 160}, {"source": 42, "target": 161}, {"source": 43, "target": 162}, {"source": 43, "target": 163}, {"source": 43, "target": 164}, {"source": 44, "target": 165}, {"source": 45, "target": 166}, {"source": 46, "target": 167}, {"source": 47, "target": 168}, {"source": 49, "target": 169}, {"source": 49, "target": 170}, {"source": 50, "target": 171}], "depth_chart_all_data": [[{"TCMBank_ID": "TCMBANKDI000001", "name": "Stomach Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKHE003734", "name": "root of common htreewingnut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000027", "name": "taxol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000071", "name": "cordyce<PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000085", "name": "CUCURBITACIN I", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036825", "name": "Honokiol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058053", "name": "diallyl trisulfide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058205", "name": "trans-resveratrol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058408", "name": "naphthalene1", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058519", "name": "(-)-epigallocatechin-3-gallate; (-)-epigallocatechin 3-o-gallate;epigallocatechin gallate; galloyl-l-epigallocatechol; epigallocatechin 3-gallate; (-)-epigallocatechin gallate; ", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN061570", "name": "curcumin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKGE000007", "name": "IL12B", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000024", "name": "NDUFV1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000026", "name": "FYN", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000028", "name": "PHB", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000031", "name": "DNMT3A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000034", "name": "PFKFB3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000041", "name": "IRF1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000045", "name": "IRS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000047", "name": "AKT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000051", "name": "IL1B", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000052", "name": "TNF", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000053", "name": "NFKB1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000059", "name": "NGFR", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000068", "name": "VEGFC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000069", "name": "CCL17", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000070", "name": "CAV1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000072", "name": "CXCL12", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000075", "name": "IL1RN", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000117", "name": "RUNX3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000121", "name": "KCNH2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000130", "name": "TOP2A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000131", "name": "CDC25C", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000135", "name": "CXCR4", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000136", "name": "MKI67", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000137", "name": "PLAU", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000152", "name": "RANBP10", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000158", "name": "RHOA", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000166", "name": "EP300", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000171", "name": "DNMT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000173", "name": "BRAF", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000175", "name": "FBXW7", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000182", "name": "B4galnt2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000196", "name": "FAS", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000199", "name": "ICAM1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000201", "name": "ESR1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000204", "name": "CXCL8", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000205", "name": "NOS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000224", "name": "AGT", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000230", "name": "ADIPOQ", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000233", "name": "DES", "record_type": "Targets"}], [{"TCMBank_ID": "TCMBANKHE003734", "name": "root of common htreewingnut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000041", "name": "Isoxanthohumol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN001948", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000004", "name": "Chronic pain", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000006", "name": "Nasopharyngeal Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000022", "name": "Prostatic Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000053", "name": "leukemia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000082", "name": "Nasopharyngeal carcinoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000085", "name": "Inflammation", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000086", "name": "Colonic Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000090", "name": "Acute kidney injury", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN000027", "name": "taxol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE005103", "name": "Himalayan Yew", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE005116", "name": "Japanese Yew", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000053", "name": "NFKB1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000149", "name": "MAP3K12", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN000071", "name": "cordyce<PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE001388", "name": "<PERSON><PERSON><PERSON> persi<PERSON>e", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE002788", "name": "<PERSON><PERSON><PERSON> pruni immaturus", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE001037", "name": "MMP2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE001073", "name": "PTGS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN000085", "name": "CUCURBITACIN I", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE004517", "name": "Largeseed Hemsleya", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004640", "name": "Rocket Candytuft", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000727", "name": "MAP1LC3A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000920", "name": "GADD45A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000047", "name": "Neoplasm Metastasis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN036825", "name": "Honokiol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE002025", "name": "Prepared bark of Officinal magnolia", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE002734", "name": "Magnolia officinalis Rehd. E<PERSON> W<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000118", "name": "CAPN2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000139", "name": "POR", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN058053", "name": "diallyl trisulfide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE002044", "name": "Rice Bean", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004071", "name": "Garlic;Allium sativum", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000017", "name": "MAPT", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000047", "name": "AKT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN058205", "name": "trans-resveratrol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE000430", "name": "Caesalpinia", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE000854", "name": "<PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000003", "name": "ASAH1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000005", "name": "UGCG", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN058408", "name": "naphthalene1", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE004258", "name": "root of Chinese Angelica", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE004583", "name": "English Walnut Seed", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000052", "name": "TNF", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000204", "name": "CXCL8", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000022", "name": "Prostatic Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN058519", "name": "(-)-epigallocatechin-3-gallate; (-)-epigallocatechin 3-o-gallate;epigallocatechin gallate; galloyl-l-epigallocatechol; epigallocatechin 3-gallate; (-)-epigallocatechin gallate; ", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE003524", "name": "Emblic Leafflower Fruit", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE005221", "name": "Ginkgo seed;Ginkgo Nut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000002", "name": "CXCL3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000017", "name": "MAPT", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKIN061570", "name": "curcumin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKHE000214", "name": "Zedoray Rhizome", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE000590", "name": "Zedoary Turmeric Equivalent plant: Curcuma k<PERSON>nsis", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKGE000013", "name": "G6PC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKGE000014", "name": "PCK1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000007", "name": "IL12B", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN016721", "name": "phorbol diester", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036842", "name": "piperine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000024", "name": "NDUFV1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN008052", "name": "Cardiolipin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN012700", "name": "coumestrol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000038", "name": "Vomiting", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000080", "name": "Anemia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000450", "name": "Hypertrophic Cardiomyopathy", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000026", "name": "FYN", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN016721", "name": "phorbol diester", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN021186", "name": "hexitol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000019", "name": "Parkinson Disease", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000022", "name": "Prostatic Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000023", "name": "<PERSON><PERSON>, Systemic", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000028", "name": "PHB", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN059684", "name": "Vitamin E β-", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN059746", "name": "α-tocopherol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000017", "name": "Liver Cirrhosis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000031", "name": "DNMT3A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN035072", "name": "3,4-benzopyrene", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058205", "name": "trans-resveratrol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000004", "name": "Chronic pain", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000034", "name": "PFKFB3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN058129", "name": "quercetin-3-O-β-D-glucuronide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000018", "name": "Pancreatic Neoplasm", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000026", "name": "Obesity", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000047", "name": "Neoplasm Metastasis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000041", "name": "IRF1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN035072", "name": "3,4-benzopyrene", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036067", "name": "vanadium", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000045", "name": "IRS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN036918", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000018", "name": "Pancreatic Neoplasm", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000026", "name": "Obesity", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000027", "name": "Cardiovascular Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000031", "name": "Insulin Resistance", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000047", "name": "AKT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003726", "name": "Cassia Bark", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003732", "name": "<PERSON><PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000004", "name": "quercetagetin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000013", "name": "bufalin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000051", "name": "IL1B", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003734", "name": "root of common htreewingnut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003737", "name": "Common Tobacco", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000029", "name": "colchicine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000047", "name": "Chelerythrine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000052", "name": "TNF", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003734", "name": "root of common htreewingnut", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003737", "name": "Common Tobacco", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000001", "name": "sa<PERSON><PERSON>in a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000004", "name": "quercetagetin", "record_type": "Ingredients"}], [{"TCMBank_ID": "TCMBANKGE000053", "name": "NFKB1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003724", "name": "rhizome of Common Turmeric", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKHE003730", "name": "<PERSON><PERSON><PERSON> Rhozi<PERSON>", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000004", "name": "quercetagetin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000008", "name": "ergothioneine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000059", "name": "NGFR", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000005", "name": "tetrodotoxin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000015", "name": "<PERSON><PERSON>, Oral", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000068", "name": "VEGFC", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN036916", "name": "Artemisinin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000006", "name": "Nasopharyngeal Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000017", "name": "Liver Cirrhosis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000069", "name": "CCL17", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN036835", "name": "w<PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN061952", "name": "Crude fiber", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000012", "name": "Immunosuppression", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000015", "name": "<PERSON><PERSON>, Oral", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000070", "name": "CAV1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN058519", "name": "(-)-epigallocatechin-3-gallate; (-)-epigallocatechin 3-o-gallate;epigallocatechin gallate; galloyl-l-epigallocatechol; epigallocatechin 3-gallate; (-)-epigallocatechin gallate; ", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000072", "name": "CXCL12", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000028", "name": "coumarin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN008636", "name": "acetylcholine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000004", "name": "Chronic pain", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000075", "name": "IL1RN", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN036795", "name": "punicalagin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036801", "name": "enterolactone", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000117", "name": "RUNX3", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN058322", "name": "baicalein", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000121", "name": "KCNH2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000006", "name": "evodiamine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000041", "name": "Isoxanthohumol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000026", "name": "Obesity", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000027", "name": "Cardiovascular Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000028", "name": "Body mass index", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000130", "name": "TOP2A", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000074", "name": "plumbagin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN003665", "name": "evoden", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000012", "name": "Immunosuppression", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000131", "name": "CDC25C", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000023", "name": "acetyl-11-keto-β-boswellicacid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036817", "name": "atractylenolide I", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000022", "name": "Prostatic Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000135", "name": "CXCR4", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000028", "name": "coumarin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000030", "name": "cocaine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000136", "name": "MKI67", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN058408", "name": "naphthalene1", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000022", "name": "Prostatic Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000137", "name": "PLAU", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN058408", "name": "naphthalene1", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000017", "name": "Liver Cirrhosis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000152", "name": "RANBP10", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN058127", "name": "quercetin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI003326", "name": "High density lipoprotein measurement", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI007408", "name": "Corpuscular Hemoglobin Concentration Mean", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI007749", "name": "Hematocrit procedure", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI008850", "name": "Malignant neoplasm of stomach", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000158", "name": "RHOA", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN001521", "name": "Potassium chloride", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN012299", "name": "potassium", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000022", "name": "Prostatic Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000166", "name": "EP300", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN036829", "name": "ursolic acid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000017", "name": "Liver Cirrhosis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000018", "name": "Pancreatic Neoplasm", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000171", "name": "DNMT1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000005", "name": "tetrodotoxin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000011", "name": "cannabigerol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000173", "name": "BRAF", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN036829", "name": "ursolic acid", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000175", "name": "FBXW7", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN035072", "name": "3,4-benzopyrene", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN057946", "name": "IFP; glycerol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000182", "name": "B4galnt2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN036830", "name": "triptonide", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000047", "name": "Neoplasm Metastasis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000048", "name": "Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000053", "name": "leukemia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000813", "name": "CAMPOMELIC DYSPLASIA", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000196", "name": "FAS", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003735", "name": "all-grass of Heartleaf Houttuynia", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000065", "name": "dioscin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036831", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000199", "name": "ICAM1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003737", "name": "Common Tobacco", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000052", "name": "cannabidiol", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000065", "name": "dioscin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000201", "name": "ESR1", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000065", "name": "dioscin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036909", "name": "Notoginsenoside R1", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000204", "name": "CXCL8", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003737", "name": "Common Tobacco", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKIN000001", "name": "sa<PERSON><PERSON>in a", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000021", "name": "agrimoniin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000205", "name": "NOS2", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKHE003752", "name": "root of Ligulilobe sage", "record_type": "<PERSON><PERSON>"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000224", "name": "AGT", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN000028", "name": "coumarin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN000047", "name": "Chelerythrine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000002", "name": "Hyperalgesia", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000230", "name": "ADIPOQ", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN033678", "name": "caffeine", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN036913", "name": "naringenin", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000003", "name": "Encephalitis", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000005", "name": "Autoimmune Diseases", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}], [{"TCMBank_ID": "TCMBANKGE000233", "name": "DES", "record_type": "Targets"}, {"TCMBank_ID": "TCMBANKIN057911", "name": "se", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKIN058519", "name": "(-)-epigallocatechin-3-gallate; (-)-epigallocatechin 3-o-gallate;epigallocatechin gallate; galloyl-l-epigallocatechol; epigallocatechin 3-gallate; (-)-epigallocatechin gallate; ", "record_type": "Ingredients"}, {"TCMBank_ID": "TCMBANKDI000007", "name": "melanoma", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000008", "name": "Brain Neoplasms", "record_type": "Diseases"}, {"TCMBank_ID": "TCMBANKDI000009", "name": "Glioma", "record_type": "Diseases"}]]}, "url": ""}