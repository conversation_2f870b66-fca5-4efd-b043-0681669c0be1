#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCM Knowledge Graph Framework 集成演示
展示中医知识图谱与面部健康分析的集成效果
"""

import cv2
import numpy as np
from datetime import datetime
import sys
import os

# 导入TCM集成模块
try:
    from tcm_ai_integration import TCMHealthIntegration, get_tcm_integration
    print("✅ [TCM-AI] TCM知识图谱集成模块加载成功")
except ImportError as e:
    print(f"❌ [TCM-AI] 模块导入失败: {e}")
    sys.exit(1)

def create_demo_face_data():
    """创建演示用的面部数据"""
    return {
        "face_detected": True,
        "face_analysis": {
            "face_color": {
                "dominant_color": "yellow",
                "brightness": 120,
                "saturation": 45
            },
            "eye_analysis": {
                "fatigue_level": "轻度疲劳",
                "brightness": 85
            },
            "lip_analysis": {
                "color_type": "淡红",
                "moisture": 60
            },
            "skin_health": {
                "uniformity": 75,
                "texture_score": 80
            }
        },
        "biological_age": {
            "estimated_age": 28,
            "confidence": 0.82
        }
    }

def demo_tcm_analysis():
    """演示TCM分析功能"""
    print("\n🏥 ===== TCM Knowledge Graph Framework 集成演示 =====")
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 初始化TCM集成系统
    print("\n1️⃣ 初始化TCM健康监测集成系统...")
    try:
        tcm_integration = get_tcm_integration()
        if tcm_integration:
            print("✅ TCM集成系统初始化成功")
            
            # 获取集成状态
            status = tcm_integration.get_integration_status()
            print(f"   - 集成状态: {'✅ 启用' if status['integration_enabled'] else '❌ 禁用'}")
            print(f"   - 知识图谱: {'✅ 已加载' if status['knowledge_graph_loaded'] else '❌ 未加载'}")
            print(f"   - 实体类型: {status['entity_types_count']}种")
        else:
            print("❌ TCM集成系统初始化失败")
            return
    except Exception as e:
        print(f"❌ 初始化异常: {e}")
        return
    
    # 2. 创建演示数据
    print("\n2️⃣ 创建演示面部分析数据...")
    demo_data = create_demo_face_data()
    print("✅ 演示数据创建完成")
    print(f"   - 面部检测: {'✅ 成功' if demo_data['face_detected'] else '❌ 失败'}")
    print(f"   - 面色类型: {demo_data['face_analysis']['face_color']['dominant_color']}")
    print(f"   - 估计年龄: {demo_data['biological_age']['estimated_age']}岁")
    
    # 3. 执行TCM增强分析
    print("\n3️⃣ 执行TCM知识图谱增强分析...")
    try:
        enhanced_results = tcm_integration.enhance_health_analysis(demo_data)
        print("✅ TCM增强分析完成")
        
        # 显示TCM分析结果
        if "tcm_analysis" in enhanced_results:
            tcm_data = enhanced_results["tcm_analysis"]
            print(f"\n🩺 中医面诊结果:")
            print(f"   • 主要证候: {tcm_data.get('main_syndrome', '未知')}")
            print(f"   • 体质类型: {tcm_data.get('constitution_type', '未知')}")
            print(f"   • 分析置信度: {tcm_data.get('confidence', 0):.2f}")
            
            # 面诊详情
            face_diagnosis = tcm_data.get('face_diagnosis', {})
            if face_diagnosis:
                print(f"\n👁️ 面诊详情:")
                print(f"   • 面色分析: {face_diagnosis.get('face_color_analysis', '无')}")
                print(f"   • 眼部状态: {face_diagnosis.get('eye_condition', '无')}")
                print(f"   • 唇部状态: {face_diagnosis.get('lip_condition', '无')}")
            
            # 中药推荐
            herbs = tcm_data.get('recommended_herbs', [])
            if herbs:
                print(f"\n🌿 中药推荐 ({len(herbs)}种):")
                for i, herb in enumerate(herbs[:5], 1):
                    herb_name = herb.get('name', '未知')
                    herb_nature = herb.get('nature', '未知')
                    print(f"   {i}. {herb_name} ({herb_nature})")
            
            # 健康建议
            suggestions = tcm_data.get('health_suggestions', [])
            if suggestions:
                print(f"\n💡 健康建议 ({len(suggestions)}条):")
                for i, suggestion in enumerate(suggestions[:5], 1):
                    print(f"   {i}. {suggestion}")
        
    except Exception as e:
        print(f"❌ TCM分析异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 展示知识图谱信息
    print("\n4️⃣ TCM知识图谱信息:")
    try:
        analyzer = tcm_integration.tcm_analyzer
        if hasattr(analyzer, 'tcm_knowledge'):
            knowledge = analyzer.tcm_knowledge
            print(f"   • 中药数据库: {len(knowledge.get('herbs', {}))}种中药")
            print(f"   • 症状数据库: {len(knowledge.get('symptoms', {}))}种症状")
            print(f"   • 证候数据库: {len(knowledge.get('syndromes', {}))}种证候")
            
            # 显示部分中药示例
            herbs = knowledge.get('herbs', {})
            if herbs:
                print(f"\n🌿 中药示例 (前5种):")
                for i, (herb_name, herb_info) in enumerate(list(herbs.items())[:5], 1):
                    nature = herb_info.get('nature', '未知')
                    effects = herb_info.get('effects', [])
                    effects_str = ', '.join(effects[:2]) if effects else '无'
                    print(f"   {i}. {herb_name} - 性味:{nature}, 功效:{effects_str}")
        
    except Exception as e:
        print(f"⚠️ 知识图谱信息获取异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 TCM Knowledge Graph Framework 集成演示完成！")
    print("\n📋 演示总结:")
    print("✅ 成功集成TCM知识图谱到健康监测系统")
    print("✅ 实现基于传统中医理论的面部分析")
    print("✅ 提供个性化中药推荐和健康建议")
    print("✅ 结合现代AI技术与传统中医智慧")
    
    print(f"\n💡 提示: 运行 'python advanced_health_monitor.py' 启动完整的健康监测系统")

if __name__ == "__main__":
    try:
        demo_tcm_analysis()
    except KeyboardInterrupt:
        print("\n\n⚠️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
