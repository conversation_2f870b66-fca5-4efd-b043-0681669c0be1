
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named pwd - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), posixpath (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed), psutil (optional), backports.tarfile (optional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._distutils.tests.unix_compat (optional), setuptools._distutils.tests.test_util (delayed)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), backports.tarfile (optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), setuptools._distutils.tests.unix_compat (optional)
missing module named posix - imported by shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional), posixpath (optional)
missing module named resource - imported by posix (top-level), test.support (delayed, conditional, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named org - imported by pickle (optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named pytest - imported by scipy._lib._testutils (delayed), pandas._testing._io (delayed), pandas._testing (delayed), h5py.tests (delayed, optional), setuptools._distutils.tests.support (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.unix_compat (top-level), setuptools._distutils.tests.test_bdist_dumb (top-level), setuptools._distutils.tests.test_bdist_rpm (top-level), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_check (top-level), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_core (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_extension (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_data (top-level), setuptools._distutils.tests.test_install_headers (top-level), setuptools._distutils.tests.test_install_lib (top-level), setuptools._distutils.tests.test_modified (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_util (top-level), setuptools._distutils.tests.test_version (top-level)
missing module named path - imported by setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_text_file (top-level)
missing module named 'jaraco.path' - imported by setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_text_file (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), pygments.formatters.img (optional)
missing module named test.support.unlink - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.skip_unless_symlink - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.rmtree - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.EnvironmentVarGuard - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.DirsOnSysPath - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.CleanImport - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), absl.flags._helpers (optional)
missing module named distutils.tests.missing_compiler_executable - imported by distutils.tests (top-level), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_install (top-level)
missing module named importlib_resources - imported by jaraco.text (optional)
missing module named 'jaraco.envs' - imported by setuptools._distutils.tests.test_sysconfig (top-level)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed), setuptools._distutils.tests.test_modified (top-level)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_log (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional), pstats (conditional, optional), tensorflow.python.debug.cli.readline_ui (top-level), dill.source (delayed, conditional, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), setuptools._distutils.dist (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional), setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional), setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.linalg._decomp (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.linalg._decomp_schur (top-level), scipy.stats._stats_py (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), scipy.signal._spline_filters (top-level), scipy.signal._filter_design (top-level), scipy.signal._lti_conversion (top-level), dill._objects (optional)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level), pandas.compat.numpy.function (top-level), dill._dill (delayed)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy.optimize._minpack_py (top-level), dill._dill (delayed)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named threadpoolctl - imported by numpy.lib.utils (delayed, optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.linalg._decomp (top-level), scipy.optimize._optimize (top-level), scipy.interpolate._pade (top-level), scipy.signal._lti_conversion (top-level)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.stats._mstats_extras (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.signal._spline_filters (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), dill._objects (optional)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named _ufunc - imported by numpy._typing (conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), dill._dill (delayed), dill._objects (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named chardet - imported by requests (optional), pygments.lexer (delayed, conditional, optional), bs4.dammit (optional)
missing module named docutils - imported by setuptools._distutils.command.check (top-level), distutils.command.check (optional)
missing module named 'docutils.parsers' - imported by setuptools._distutils.command.check (top-level), distutils.command.check (optional)
missing module named 'docutils.utils' - imported by setuptools._distutils.command.check (top-level), distutils.command.check (optional)
missing module named xx - imported by setuptools._distutils.tests.test_build_ext (delayed)
missing module named 'docutils.nodes' - imported by setuptools._distutils.command.check (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named fcntl - imported by subprocess (optional), absl.flags._helpers (optional), filelock._unix (conditional, optional), pty (delayed, optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (top-level)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named IPython - imported by matplotlib.pyplot (delayed, conditional), matplotlib.backend_bases (delayed), jax._src.traceback_util (delayed), jax._src.debugger.colab_lib (conditional), pandas.io.formats.printing (delayed), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level), keras.src.utils.vis_utils (delayed, conditional, optional), tensorflow.python.keras.utils.vis_utils (delayed, conditional, optional)
missing module named pydot - imported by keras.src.utils.vis_utils (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named pydotplus - imported by keras.src.utils.vis_utils (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named pydot_ng - imported by keras.src.utils.vis_utils (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named 'tensorflow.compat' - imported by tensorboard.util.op_evaluator (delayed), tensorboard.util.encoder (delayed), tensorboard.plugins.audio.summary (delayed), tensorboard.plugins.custom_scalar.summary (delayed), tensorboard.plugins.histogram.summary (delayed), tensorboard.plugins.image.summary (delayed), tensorboard.plugins.pr_curve.summary (delayed), tensorboard.plugins.scalar.summary (delayed), tensorboard.plugins.text.summary (delayed), keras.src.engine.functional (top-level), keras.src.backend (top-level), keras.src.backend_config (top-level), keras.src.distribute.distribute_coordinator_utils (top-level), keras.src.dtensor (conditional), keras.src.engine.keras_tensor (top-level), keras.src.saving.serialization_lib (top-level), keras.src.saving.legacy.serialization (top-level), keras.src.utils.tf_contextlib (top-level), keras.src.utils.tf_inspect (top-level), keras.src.saving.legacy.saved_model.utils (top-level), keras.src.engine.base_layer_utils (top-level), keras.src.utils.control_flow_util (top-level), keras.src.utils.tf_utils (top-level), keras.src.metrics.base_metric (top-level), keras.src.dtensor.utils (top-level), keras.src.engine.base_layer (top-level), keras.src.constraints (top-level), keras.src.initializers (top-level), keras.src.initializers.initializers (top-level), keras.src.initializers.initializers_v1 (top-level), keras.src.utils.generic_utils (top-level), keras.src.regularizers (top-level), keras.src.engine.input_spec (top-level), keras.src.engine.node (top-level), keras.src.saving.legacy.saved_model.json_utils (top-level), keras.src.mixed_precision.loss_scale_optimizer (top-level), keras.src.optimizers (top-level), keras.src.optimizers.adadelta (top-level), keras.src.optimizers.optimizer (top-level), keras.src.optimizers.utils (top-level), keras.src.optimizers.schedules.learning_rate_schedule (top-level), keras.src.optimizers.adafactor (top-level), keras.src.optimizers.adagrad (top-level), keras.src.optimizers.adam (top-level), keras.src.optimizers.adamax (top-level), keras.src.optimizers.adamw (top-level), keras.src.optimizers.ftrl (top-level), keras.src.optimizers.lion (top-level), keras.src.optimizers.nadam (top-level), keras.src.optimizers.rmsprop (top-level), keras.src.optimizers.sgd (top-level), keras.src.optimizers.legacy.adadelta (top-level), keras.src.optimizers.legacy.optimizer_v2 (top-level), keras.src.utils.layer_utils (top-level), keras.src.optimizers.legacy.adagrad (top-level), keras.src.optimizers.legacy.adam (top-level), keras.src.optimizers.legacy.adamax (top-level), keras.src.optimizers.legacy.ftrl (top-level), keras.src.optimizers.legacy.gradient_descent (top-level), keras.src.optimizers.legacy.nadam (top-level), keras.src.optimizers.legacy.rmsprop (top-level), keras.src.optimizers.optimizer_v1 (top-level), keras.src.mixed_precision.policy (top-level), keras.src.mixed_precision.device_compatibility_check (top-level), keras.src.mixed_precision.autocast_variable (top-level), keras.src.distribute.distributed_training_utils (top-level), keras.src.saving.legacy.saved_model.layer_serialization (top-level), keras.src.saving.legacy.saved_model.save_impl (top-level), keras.src.saving.legacy.saving_utils (top-level), keras.src.losses (top-level), keras.src.saving.saving_lib (top-level), keras.src.utils.losses_utils (top-level), keras.src.utils.version_utils (top-level), keras.src.engine.compile_utils (top-level), keras.src.saving.legacy.saved_model.load (top-level), keras.src.layers (top-level), keras.src.engine.base_preprocessing_layer (top-level), keras.src.engine.data_adapter (top-level), keras.src.engine.training_utils (top-level), keras.src.utils.data_utils (top-level), keras.src.utils.dataset_creator (top-level), keras.src.engine.input_layer (top-level), keras.src.utils.traceback_utils (top-level), keras.src.layers.activation.softmax (top-level), keras.src.layers.activation.thresholded_relu (top-level), keras.src.layers.attention.additive_attention (top-level), keras.src.layers.attention.base_dense_attention (top-level), keras.src.layers.attention.attention (top-level), keras.src.layers.attention.multi_head_attention (top-level), keras.src.activations (top-level), keras.src.layers.core.dense (top-level), keras.src.layers.core.einsum_dense (top-level), keras.src.layers.core.embedding (top-level), keras.src.layers.core.identity (top-level), keras.src.layers.core.lambda_layer (top-level), keras.src.layers.core.masking (top-level), keras.src.layers.core.tf_op_layer (top-level), keras.src.layers.regularization.alpha_dropout (top-level), keras.src.layers.regularization.dropout (top-level), keras.src.layers.regularization.gaussian_dropout (top-level), keras.src.layers.regularization.gaussian_noise (top-level), keras.src.layers.regularization.spatial_dropout1d (top-level), keras.src.layers.regularization.spatial_dropout2d (top-level), keras.src.layers.regularization.spatial_dropout3d (top-level), keras.src.layers.reshaping.cropping1d (top-level), keras.src.utils.conv_utils (top-level), keras.src.layers.reshaping.cropping2d (top-level), keras.src.layers.reshaping.cropping3d (top-level), keras.src.layers.reshaping.flatten (top-level), keras.src.layers.reshaping.permute (top-level), keras.src.layers.reshaping.repeat_vector (top-level), keras.src.layers.reshaping.reshape (top-level), keras.src.layers.reshaping.up_sampling1d (top-level), keras.src.layers.reshaping.up_sampling2d (top-level), keras.src.utils.image_utils (top-level), keras.src.layers.reshaping.up_sampling3d (top-level), keras.src.layers.reshaping.zero_padding1d (top-level), keras.src.layers.reshaping.zero_padding2d (top-level), keras.src.layers.reshaping.zero_padding3d (top-level), keras.src.layers.convolutional.base_conv (top-level), keras.src.layers.convolutional.separable_conv1d (top-level), keras.src.layers.convolutional.base_separable_conv (top-level), keras.src.layers.convolutional.conv1d_transpose (top-level), keras.src.layers.convolutional.conv2d_transpose (top-level), keras.src.layers.convolutional.conv3d_transpose (top-level), keras.src.layers.convolutional.depthwise_conv1d (top-level), keras.src.layers.convolutional.base_depthwise_conv (top-level), keras.src.layers.convolutional.separable_conv2d (top-level), keras.src.layers.pooling.base_pooling1d (top-level), keras.src.layers.pooling.average_pooling2d (top-level), keras.src.layers.pooling.base_pooling2d (top-level), keras.src.layers.pooling.average_pooling3d (top-level), keras.src.layers.pooling.base_pooling3d (top-level), keras.src.layers.pooling.global_average_pooling1d (top-level), keras.src.layers.pooling.base_global_pooling1d (top-level), keras.src.layers.pooling.base_global_pooling2d (top-level), keras.src.layers.pooling.base_global_pooling3d (top-level), keras.src.layers.pooling.max_pooling2d (top-level), keras.src.layers.pooling.max_pooling3d (top-level), keras.src.layers.locally_connected.locally_connected_utils (top-level), keras.src.layers.merging.base_merge (top-level), keras.src.layers.merging.concatenate (top-level), keras.src.layers.merging.dot (top-level), keras.src.layers.merging.maximum (top-level), keras.src.layers.merging.minimum (top-level), keras.src.layers.normalization.batch_normalization (top-level), keras.src.layers.normalization.group_normalization (top-level), keras.src.layers.normalization.layer_normalization (top-level), keras.src.layers.normalization.unit_normalization (top-level), keras.src.layers.normalization.spectral_normalization (top-level), keras.src.layers.rnn (top-level), keras.src.layers.rnn.rnn_utils (top-level), keras.src.layers.rnn.base_rnn (top-level), keras.src.layers.rnn.dropout_rnn_cell_mixin (top-level), keras.src.layers.rnn.stacked_rnn_cells (top-level), keras.src.layers.rnn.simple_rnn (top-level), keras.src.layers.rnn.gru (top-level), keras.src.layers.rnn.gru_lstm_utils (top-level), keras.src.layers.rnn.lstm (top-level), keras.src.layers.rnn.bidirectional (top-level), keras.src.layers.rnn.cell_wrappers (top-level), keras.src.layers.serialization (top-level), keras.src.layers.preprocessing.category_encoding (top-level), keras.src.layers.preprocessing.preprocessing_utils (top-level), keras.src.layers.preprocessing.discretization (top-level), keras.src.layers.preprocessing.hashed_crossing (top-level), keras.src.layers.preprocessing.hashing (top-level), keras.src.layers.preprocessing.image_preprocessing (top-level), keras.src.layers.preprocessing.integer_lookup (top-level), keras.src.layers.preprocessing.index_lookup (top-level), keras.src.layers.preprocessing.normalization (top-level), keras.src.layers.preprocessing.string_lookup (top-level), keras.src.layers.preprocessing.text_vectorization (top-level), keras.src.feature_column.sequence_feature_column (top-level), keras.src.feature_column.base_feature_layer (top-level), keras.src.premade_models.linear (top-level), keras.src.engine.training (top-level), keras.src.callbacks (top-level), keras.src.distribute.distributed_file_utils (top-level), keras.src.distribute.worker_training_state (top-level), keras.src.dtensor.layout_map (top-level), keras.src.saving.pickle_utils (top-level), keras.src.saving.saving_api (top-level), keras.src.saving.legacy.save (top-level), keras.src.saving.legacy.hdf5_format (top-level), keras.src.saving.legacy.saved_model.load_context (top-level), keras.src.saving.legacy.saved_model.save (top-level), keras.src.engine.sequential (top-level), keras.src.export.export_lib (top-level), keras.src.engine.training_v1 (top-level), keras.src.distribute.distributed_training_utils_v1 (top-level), keras.src.engine.training_utils_v1 (top-level), keras.src.engine.training_arrays_v1 (top-level), keras.src.engine.training_distributed_v1 (top-level), keras.src.engine.partial_batch_padding_handler (top-level), keras.src.engine.training_eager_v1 (top-level), keras.src.engine.training_generator_v1 (top-level), keras.src.premade_models.wide_deep (top-level), keras.src.feature_column.dense_features_v2 (top-level), keras.src.feature_column.dense_features (top-level), keras.src.layers.rnn.base_conv_lstm (top-level), keras.src.layers.rnn.base_conv_rnn (top-level), keras.src.layers.rnn.cudnn_gru (top-level), keras.src.layers.rnn.base_cudnn_rnn (top-level), keras.src.layers.rnn.cudnn_lstm (top-level), keras.src.layers.rnn.time_distributed (top-level), keras.src.layers.kernelized (top-level), keras.src.saving.legacy.saved_model.serialized_attributes (top-level), keras.src.utils.metrics_utils (top-level), keras.src.saving.legacy.saved_model.metric_serialization (top-level), keras.src.metrics.py_metric (top-level), keras.src.metrics.accuracy_metrics (top-level), keras.src.metrics.probabilistic_metrics (top-level), keras.src.metrics.regression_metrics (top-level), keras.src.metrics.confusion_metrics (top-level), keras.src.metrics.f_score_metrics (top-level), keras.src.metrics.iou_metrics (top-level), keras.src.utils.audio_dataset (top-level), keras.src.utils.dataset_utils (top-level), keras.src.utils.text_dataset (top-level), keras.src.utils.timeseries_dataset (top-level), keras.src.utils.image_dataset (top-level), keras.src.utils.feature_space (top-level), keras.src.utils.vis_utils (top-level), keras.src.engine.functional_utils (top-level), keras.src.models.cloning (top-level), keras.src.models.sharpness_aware_minimization (top-level), keras.src.applications.convnext (top-level), keras.src.applications.densenet (top-level), keras.src.applications.efficientnet (top-level), keras.src.applications.efficientnet_v2 (top-level), keras.src.applications.inception_resnet_v2 (top-level), keras.src.applications.inception_v3 (top-level), keras.src.applications.mobilenet (top-level), keras.src.applications.mobilenet_v2 (top-level), keras.src.applications.mobilenet_v3 (top-level), keras.src.applications.nasnet (top-level), keras.src.applications.resnet (top-level), keras.src.applications.resnet_rs (top-level), keras.src.applications.vgg16 (top-level), keras.src.applications.vgg19 (top-level), keras.src.applications.xception (top-level), keras.src.applications.regnet (top-level), keras.src.estimator (top-level), keras.src.legacy_tf_layers.migration_utils (top-level), keras.src.legacy_tf_layers.base (top-level), keras.src.engine.base_layer_v1 (top-level), keras.src.legacy_tf_layers.variable_scope_shim (top-level), keras.src.legacy_tf_layers.convolutional (top-level), keras.src.legacy_tf_layers.core (top-level), keras.src.legacy_tf_layers.normalization (top-level), keras.src.layers.rnn.legacy_cell_wrappers (top-level), keras.src.layers.rnn.legacy_cells (top-level), keras.src.callbacks_v1 (top-level), keras.src.benchmarks.distribution_util (top-level), keras.src.distribute.dataset_creator_model_fit_test_base (top-level), keras.src.distribute.keras_correctness_test_base (top-level), keras.src.distribute.strategy_combinations (top-level), keras.src.distribute.model_combinations (top-level), keras.src.distribute.simple_models (top-level), keras.src.distribute.multi_worker_testing_utils (top-level), keras.src.distribute.optimizer_combinations (top-level), keras.src.distribute.saved_model_test_base (top-level), keras.src.distribute.test_example (top-level), keras.src.distribute.tpu_strategy_test_utils (top-level), keras.src.dtensor.integration_test_utils (top-level), keras.src.dtensor.test_util (top-level), keras.src.layers.preprocessing.benchmarks.bucketized_column_dense_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.feature_column_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_encoding_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_hash_dense_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_hash_varlen_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_vocab_file_dense_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_vocab_file_varlen_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_vocab_list_dense_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_vocab_list_indicator_dense_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_vocab_list_indicator_varlen_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.category_vocab_list_varlen_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.discretization_adapt_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.embedding_dense_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.embedding_varlen_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.hashed_crossing_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.hashing_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.image_preproc_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.index_lookup_adapt_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.index_lookup_forward_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.normalization_adapt_benchmark (top-level), keras.src.layers.preprocessing.benchmarks.weighted_embedding_varlen_benchmark (top-level), keras.src.layers.preprocessing.preprocessing_stage (top-level), keras.src.layers.preprocessing.preprocessing_test_utils (top-level), keras.src.mixed_precision.test_util (top-level), keras.src.optimizers.legacy_learning_rate_decay (top-level), keras.src.saving.legacy.saved_model.create_test_saved_model (top-level), keras.src.testing_infra.test_utils (top-level), keras.src.testing_infra.test_combinations (top-level), keras.src.tests.keras_doctest (top-level), keras.src.utils.kernelized_utils (top-level), keras.src.utils.kpl_test_utils (top-level)
missing module named "'six.moves.urllib'.request" - imported by tensorflow.python.keras.utils.data_utils (top-level), keras.src.utils.data_utils (top-level)
missing module named "'six.moves.urllib'.parse" - imported by keras.src.utils.data_utils (top-level)
missing module named tensorflow_io - imported by keras.src.utils.audio_dataset (delayed, conditional, optional)
missing module named 'IPython.utils' - imported by h5py.ipy_completer (top-level)
missing module named 'IPython.core' - imported by matplotlib.backend_bases (delayed), rich.pretty (delayed, optional), pandas.io.formats.printing (delayed, conditional), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level)
missing module named mpi4py - imported by h5py._hl.files (delayed)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'jaxlib.mlir._mlir_libs._mlir.ir' - imported by jaxlib.mlir.ir (top-level)
missing module named 'jaxlib.mlir._mlir_libs._mlir.passmanager' - imported by jaxlib.mlir.passmanager (top-level)
missing module named theano - imported by opt_einsum.backends.theano (delayed)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), astunparse (top-level), tensorflow.python.distribute.coordinator.cluster_coordinator (top-level), 'six.moves.urllib' (top-level), tensorflow_estimator.python.estimator.canned.linear_optimizer.python.utils.sdca_ops (top-level), tensorflow_estimator.python.estimator.canned.linear_optimizer.python.utils.sharded_mutable_dense_hashtable (top-level), tensorflow_estimator.python.estimator.tpu.tpu_estimator (top-level), tensorflow.python.distribute.multi_process_runner (top-level), pasta.base.annotate (top-level)
missing module named six.moves.zip - imported by six.moves (top-level), pasta.base.annotate (top-level)
missing module named six.moves.xrange - imported by six.moves (top-level), tensorflow_estimator.python.estimator.tpu.tpu_estimator (top-level)
missing module named six.moves.cStringIO - imported by six.moves (top-level), astunparse (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level), tensorflow_estimator.python.estimator.canned.linear_optimizer.python.utils.sdca_ops (top-level), tensorflow_estimator.python.estimator.canned.linear_optimizer.python.utils.sharded_mutable_dense_hashtable (top-level)
missing module named StringIO - imported by six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named PyQt6 - imported by matplotlib.backends.qt_compat (delayed, conditional), matplotlib.backends.backend_qtagg (delayed, conditional, optional)
missing module named shiboken2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named PySide2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named sip - imported by matplotlib.backends.qt_compat (delayed, conditional), PyQt5 (top-level)
missing module named shiboken6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named PySide6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named 'tornado.template' - imported by matplotlib.backends.backend_webagg (delayed)
missing module named 'tornado.websocket' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.ioloop' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.web' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named tornado - imported by matplotlib.backends.backend_webagg (optional), matplotlib.backends.backend_webagg_core (delayed)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named 'numpy.exceptions' - imported by matplotlib.cbook (optional), scipy._lib._util (conditional)
missing module named 'IPython.display' - imported by rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named ipywidgets - imported by rich.live (delayed, conditional, optional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), jax._src.compilation_cache (optional)
missing module named cloudpickle - imported by jax._src.pickle_util (optional)
missing module named etils - imported by jax._src.path (optional)
missing module named jax_plugins - imported by jax._src.xla_bridge (optional)
missing module named libtpu - imported by jax._src.cloud_tpu_init (delayed, optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional), google.auth.crypt.es256 (top-level)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional), google.auth.crypt._cryptography_rsa (top-level), google.auth.crypt.es256 (top-level)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional), google.auth.identity_pool (delayed)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named mlir - imported by jaxlib.mosaic.python.tpu (optional)
missing module named rocm - imported by jaxlib.gpu_solver (optional), jaxlib.gpu_sparse (optional), jaxlib.gpu_prng (optional), jaxlib.gpu_linalg (optional), jaxlib.gpu_triton (optional)
missing module named cuda - imported by jaxlib.gpu_solver (optional)
missing module named jax_cuda12_plugin - imported by jax._src.lib (optional)
missing module named 'jaxlib.cuda' - imported by jax._src.lib (optional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named numpy.complex128 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.complex64 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.signal._spline_filters (top-level)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named torch - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib._array_api (delayed, conditional), opt_einsum.backends.torch (delayed, conditional)
missing module named cupy - imported by opt_einsum.backends.cupy (delayed), scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named scipy.linalg.svd - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.sparse.linalg._eigen._svds (top-level), scipy.linalg._decomp_polar (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._remove_redundancy (top-level)
missing module named scipy.linalg.cholesky - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._minpack_py (top-level)
missing module named scipy.linalg.cho_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.cho_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.eigh - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy._lib.cobyqa.models (top-level)
missing module named scipy.linalg.inv - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._nonlin (top-level)
missing module named scipy.linalg.lu_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.lu_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.eig - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.linalg.lstsq - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.signal._fir_filter_design (top-level), scipy.signal._savitzky_golay (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.interpolate._bsplines (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._linprog_rs (top-level), scipy.interpolate._cubic (top-level), scipy.signal._fir_filter_design (top-level)
missing module named scipy.linalg.qr - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy._lib.cobyqa.subsolvers.optim (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._nonlin (top-level), scipy.signal._ltisys (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy._lib._array_api (delayed), scipy.optimize._numdiff (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._milp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), pandas.core.dtypes.common (delayed, conditional, optional), tensorflow.python.keras.engine.data_adapter (delayed, optional), keras.src.engine.data_adapter (delayed, optional), keras.src.engine.training_arrays_v1 (optional), keras.src.engine.training_v1 (optional), tensorflow.python.keras.engine.training_arrays_v1 (optional), tensorflow.python.keras.engine.training_v1 (optional), scipy.sparse.csgraph._validation (top-level)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.spatial.Voronoi - imported by scipy.spatial (top-level), scipy.stats._qmc (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._spline_filters (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level), scipy.signal._waveforms (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cPickle (top-level), cffi.ffiplatform (optional)
missing module named copy_reg - imported by cPickle (top-level), cStringIO (top-level)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level), scipy.signal._spline_filters (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arccosh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tan - imported by numpy (top-level), scipy.signal._spline_filters (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arctan - imported by numpy (top-level), scipy.signal._spline_filters (top-level)
missing module named web_pdb - imported by jax._src.debugger.web_debugger (delayed, conditional)
missing module named 'google.colab' - imported by jax._src.debugger.colab_lib (conditional), jax._src.debugger.colab_debugger (conditional)
missing module named 'numpy.lib.array_utils' - imported by scipy._lib.array_api_compat.common._linalg (conditional)
missing module named 'numpy.linalg._linalg' - imported by scipy._lib.array_api_compat.numpy.linalg (delayed, optional)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named cython - imported by scipy._lib._testutils (optional)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named yaml - imported by scipy.__config__ (delayed)
missing module named keras.src.initializers.TruncatedNormal - imported by keras.src.initializers (top-level), keras.src.layers.normalization.spectral_normalization (top-level)
missing module named 'google.cloud' - imported by tensorflow_estimator.python.estimator.keras_lib (delayed, optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pyarrow - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.strings.accessor (delayed, conditional), pandas.core.interchange.utils (delayed, conditional), pandas.io._util (conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'botocore.exceptions' - imported by pandas.io.common (delayed, conditional, optional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.descriptors' - imported by pandas.io.excel._openpyxl (conditional)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'lxml.etree' - imported by pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named lxml - imported by pandas.io.xml (conditional), bs4.builder._lxml (top-level)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named fsspec - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional), pandas.io.orc (conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named 'cryptography.hazmat' - imported by google.auth.crypt._cryptography_rsa (top-level), google.auth.crypt.es256 (top-level), google.auth.transport._custom_tls_signer (delayed)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named 'cryptography.exceptions' - imported by google.auth.crypt._cryptography_rsa (top-level), google.auth.crypt.es256 (top-level)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level)
missing module named portpicker - imported by tensorflow.python.framework.test_util (delayed), tensorflow.python.debug.lib.grpc_debug_test_server (top-level), keras.src.distribute.multi_worker_testing_utils (optional)
missing module named boto3 - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named botocore - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named tensorboard.compat.notf - imported by tensorboard.compat (delayed, optional)
missing module named memory_profiler - imported by tensorflow.python.eager.memory_tests.memory_test_util (optional), keras.src.benchmarks.model_memory_profile (optional)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named oauth2client - imported by tensorflow.python.distribute.cluster_resolver.gce_cluster_resolver (optional), tensorflow.python.tpu.client.client (optional)
missing module named googleapiclient - imported by tensorflow.python.distribute.cluster_resolver.gce_cluster_resolver (optional), tensorflow.python.tpu.client.client (optional)
missing module named 'tensorflow.contrib' - imported by tensorflow.python.tools.import_pb_to_tensorboard (optional)
missing module named 'keras.optimizers.optimizer_v2' - imported by tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named mock - imported by tensorflow.python.platform.test (conditional)
missing module named rules_python - imported by tensorflow.python.platform.resource_loader (optional)
missing module named tensorflow.python.keras.layers.wrappers - imported by tensorflow.python.keras.layers (delayed), tensorflow.python.keras.utils.vis_utils (delayed)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_false' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_xla_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.platform.cpp_memory_checker' - imported by tensorflow.python.framework.memory_checker (optional)
missing module named objgraph - imported by tensorflow.python.distribute.test_util (optional)
missing module named tblib - imported by tensorflow.python.distribute.multi_process_runner (optional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named diff - imported by dill._dill (delayed, conditional, optional)
missing module named dill.diff - imported by dill (delayed, conditional, optional), dill._dill (delayed, conditional, optional)
missing module named version - imported by dill (optional)
missing module named 'six.moves.urllib'.request - imported by 'six.moves.urllib' (top-level), tensorflow.python.distribute.failure_handling.failure_handling_util (top-level)
missing module named 'six.moves.urllib' - imported by 'six.moves.urllib' (top-level)
missing module named cloud_tpu_client - imported by tensorflow.python.distribute.cluster_resolver.tpu.tpu_cluster_resolver (optional)
missing module named kubernetes - imported by tensorflow.python.distribute.cluster_resolver.kubernetes_cluster_resolver (delayed, conditional, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named astn - imported by gast.ast2 (top-level)
missing module named tflite_runtime - imported by tensorflow.lite.python.metrics.metrics (conditional), tensorflow.lite.python.interpreter (conditional), tensorflow.lite.python.analyzer (conditional), tensorflow.lite.tools.visualize (conditional)
missing module named tensorflow.python.framework.fast_tensor_util - imported by tensorflow.python.framework (optional), tensorflow.python.framework.tensor_util (optional)
missing module named 'mediapipe.python._framework_bindings.validated_graph_config' - imported by mediapipe.python (top-level)
missing module named 'mediapipe.python._framework_bindings.timestamp' - imported by mediapipe.python (top-level)
missing module named 'mediapipe.python._framework_bindings.packet' - imported by mediapipe.python (top-level)
missing module named 'mediapipe.python._framework_bindings.matrix' - imported by mediapipe.python (top-level)
missing module named 'mediapipe.python._framework_bindings.image_frame' - imported by mediapipe.python (top-level)
missing module named 'mediapipe.python._framework_bindings.image' - imported by mediapipe.python (top-level)
missing module named 'mediapipe.python._framework_bindings.calculator_graph' - imported by mediapipe.python (top-level)
